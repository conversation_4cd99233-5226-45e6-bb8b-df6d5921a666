# 单车道铣底基准线创建说明

## 📋 实现概述

按照用户要求，参考车道边线的延伸方法重写了单车道铣底算法的第一步：创建延长和裁剪后的基准线。

## 🎯 用户需求

### 步骤1A：创建左A基准线
1. 从起始线的起始点出发，沿起始线的反向（向起始方向外侧）延伸一条直线
2. 延伸直线长度 = 停止线宽度 + 铣底宽度*0.5 - 车道虚线宽度*0.5
3. 将延伸直线与原始起始线连接，形成完整的延长线
4. 从延长线的结束点向起始点方向标记裁剪点
5. 裁剪点距离结束点的距离 = 铣底宽度*0.5 - 车道虚线宽度*0.5
6. 在裁剪点处分割延长线，比较裁剪点两侧线段的长度
7. 删除较短的线段部分，保留较长的线段
8. 将保留的线段命名为"左A基准线"

### 步骤1B：创建右A基准线
1. 从起始线的结束点出发，沿起始线的正向（向结束方向外侧）延伸一条直线
2. 延伸直线长度 = 停止线宽度 + 铣底宽度*0.5 - 车道虚线宽度*0.5
3. 将延伸直线与原始起始线连接，形成完整的延长线
4. 从延长线的起始点向结束点方向标记裁剪点
5. 裁剪点距离起始点的距离 = 铣底宽度*0.5 - 车道虚线宽度*0.5
6. 在裁剪点处分割延长线，比较裁剪点两侧线段的长度
7. 删除较短的线段部分，保留较长的线段
8. 将保留的线段命名为"右A基准线"

## 🔧 技术实现

### 1. 主要方法结构

#### `CreateSingleLaneMilling` - 主入口方法
```csharp
private void CreateSingleLaneMilling(Curve startLine, double lineWidth, double laneWidth, double edgeLineWidth,
                                   double millingWidth, short millingColor, List<Entity> entitiesToAdd, Editor editor)
```

**功能**：
- 步骤1：调用`CreateMillingBaseLines`创建延长和裁剪后的基准线
- 步骤2：调用`CreateMillingAreasFromBaseLines`基于基准线创建铣底区域

#### `CreateMillingBaseLines` - 基准线创建核心方法
```csharp
private (Curve leftABaseLine, Curve rightABaseLine) CreateMillingBaseLines(Curve startLine, double lineWidth, double millingWidth, Editor editor)
```

**功能**：
- 获取参数：停止线宽度(textBox13)、车道虚线宽度(textBox8)、铣底宽度(textBox9)
- 计算延伸长度和裁剪距离
- 分别创建左A和右A基准线

#### `CreateLeftABaseLine` - 左A基准线创建
```csharp
private Curve CreateLeftABaseLine(Curve startLine, double extensionLength, double trimDistance, Editor editor)
```

**实现步骤**：
1. 获取起始点的切线方向，计算反向延伸方向
2. 创建延伸直线
3. 将延伸直线与原始起始线连接形成完整延长线
4. 计算裁剪点位置
5. 使用AutoCAD的`GetSplitCurves`方法分割
6. 保留较长的线段

#### `CreateRightABaseLine` - 右A基准线创建
```csharp
private Curve CreateRightABaseLine(Curve startLine, double extensionLength, double trimDistance, Editor editor)
```

**实现步骤**：
1. 获取结束点的切线方向，计算正向延伸方向
2. 创建延伸直线
3. 将延伸直线与原始起始线连接形成完整延长线
4. 计算裁剪点位置
5. 使用AutoCAD的`GetSplitCurves`方法分割
6. 保留较长的线段

### 2. 辅助方法

#### `CreateExtendedLineFromStartToEnd` - 左A延长线创建
```csharp
private Curve CreateExtendedLineFromStartToEnd(Line extensionLine, Curve originalLine, Editor editor)
```

#### `CreateExtendedLineFromEndToExtension` - 右A延长线创建
```csharp
private Curve CreateExtendedLineFromEndToExtension(Curve originalLine, Line extensionLine, Editor editor)
```

#### `SplitAndKeepLongerSegment` - 分割并保留较长线段
```csharp
private Curve SplitAndKeepLongerSegment(Curve curve, Point3d splitPoint, Editor editor)
```

**功能**：
- 使用AutoCAD原生`GetSplitCurves`方法进行分割
- 比较分割后两段的长度
- 保留较长的线段，清理较短的线段

### 3. 参数映射

- **停止线宽度** = textBox13 (默认: 0.2)
- **车道虚线宽度** = textBox8 (默认: 0.15)
- **铣底宽度** = textBox9 (用户输入)

### 4. 计算公式

- **延伸长度** = 停止线宽度 + 铣底宽度*0.5 - 车道虚线宽度*0.5
- **裁剪距离** = 铣底宽度*0.5 - 车道虚线宽度*0.5

## ✅ 技术特点

### 1. AutoCAD原生API使用
- 使用`GetSplitCurves`方法进行精确分割
- 使用`ExtendStartLineToPosition`方法进行几何延长
- 保持原始几何特性（直线、多段线、圆弧、样条曲线等）

### 2. 几何类型支持
- **直线**：直接修改端点坐标
- **多段线**：添加新顶点
- **圆弧**：调整起始角或结束角
- **样条曲线**：使用切线方向创建延长线段
- **其他曲线类型**：使用通用延长方法

### 3. 错误处理
- 每个方法都有独立的异常处理
- 详细的调试信息输出
- 自动备用方案

### 4. 验证功能
- 临时将基准线添加到图形中进行验证
- 输出基准线类型和长度信息
- 便于用户检查基准线创建是否正确

## 📊 编译结果

✅ **编译成功**：无错误，仅有8个未使用变量警告
✅ **功能完整**：基准线创建逻辑完全实现
✅ **代码结构**：清晰的方法分层和职责分离

## 🔄 下一步计划

1. **用户测试**：测试基准线创建是否符合预期
2. **完善步骤2**：基于基准线创建左A、左B、右A、右B铣底区域
3. **性能优化**：根据测试结果进行必要的性能调整
4. **功能验证**：确保所有铣底功能正常工作

## 📝 注意事项

- 当前实现为第一步（基准线创建），第二步（铣底区域创建）待后续完善
- 基准线会临时显示在图形中，便于验证延长和裁剪是否正确
- 所有计算都基于用户提供的精确公式和技术要求
- 严格遵循AutoCAD原生API方法，确保几何精度和兼容性
