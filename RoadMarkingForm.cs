using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Microsoft.Win32;
using AcApp = Autodesk.AutoCAD.ApplicationServices.Application;

namespace RoadMarkingPlugin
{
    public partial class RoadMarkingForm : Form
    {
        // 注册表键路径
        private const string REGISTRY_KEY = @"SOFTWARE\RoadMarkingPlugin";

        public RoadMarkingForm()
        {
            this.InitializeComponent();
            LoadSettings(); // 加载保存的设置

            // 注册窗体关闭事件
            this.FormClosing += RoadMarkingForm_FormClosing;

            // 设置计算模式切换事件处理程序
            SetupCalculationModeControls();
        }

        // 窗体关闭时保存设置
        private void RoadMarkingForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            SaveSettings(); // 保存设置

            // 如果是用户点击关闭按钮，改为隐藏窗体而不是真正关闭
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                this.Hide();
            }
        }

        // 保存设置到注册表
        private void SaveSettings()
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        // 保存所有文本框的值
                        key.SetValue("textBox1", this.textBox1.Text);
                        key.SetValue("textBox2", this.textBox2.Text);
                        key.SetValue("textBox3", this.textBox3.Text);
                        key.SetValue("textBox同向车道数", this.textBox同向车道数.Text);
                        key.SetValue("textBox4", this.textBox4.Text);
                        key.SetValue("textBox5", this.textBox5.Text);
                        key.SetValue("textBox6", this.textBox6.Text);
                        key.SetValue("textBox7", this.textBox7.Text);
                        key.SetValue("textBox8", this.textBox8.Text);
                        key.SetValue("textBox9", this.textBox9.Text);
                        key.SetValue("textBox10", this.textBox10.Text);
                        key.SetValue("textBox11", this.textBox11.Text);
                        key.SetValue("textBox12", this.textBox12.Text);
                        key.SetValue("textBox13", this.textBox13.Text);
                        key.SetValue("textBox15", this.textBox15.Text);

                        // 保存计算模式设置
                        key.SetValue("radioButtonLaneWidth", this.radioButtonLaneWidth.Checked);
                    }
                }
            }
            catch (System.Exception ex)
            {
                // 如果保存失败，不影响程序运行
                MessageBox.Show($"保存设置失败: {ex.Message}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        // 从注册表加载设置
        private void LoadSettings()
        {
            try
            {
                using (RegistryKey key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    if (key != null)
                    {
                        // 加载所有文本框的值，如果不存在则使用默认值
                        this.textBox1.Text = key.GetValue("textBox1", "0.2").ToString();
                        this.textBox2.Text = key.GetValue("textBox2", "3.5").ToString();
                        this.textBox3.Text = key.GetValue("textBox3", "0.5").ToString();
                        this.textBox同向车道数.Text = key.GetValue("textBox同向车道数", "1").ToString();
                        this.textBox4.Text = key.GetValue("textBox4", "0.5").ToString();
                        this.textBox5.Text = key.GetValue("textBox5", "2.0").ToString();
                        this.textBox6.Text = key.GetValue("textBox6", "2.5").ToString();
                        this.textBox7.Text = key.GetValue("textBox7", "0.15").ToString();
                        this.textBox8.Text = key.GetValue("textBox8", "0.15").ToString();
                        this.textBox9.Text = key.GetValue("textBox9", "6.0").ToString();
                        this.textBox10.Text = key.GetValue("textBox10", "5").ToString();
                        this.textBox11.Text = key.GetValue("textBox11", "4.0").ToString();
                        this.textBox12.Text = key.GetValue("textBox12", "3.0").ToString();
                        this.textBox13.Text = key.GetValue("textBox13", "0.2").ToString();
                        this.textBox15.Text = key.GetValue("textBox15", "9.0").ToString();

                        // 加载计算模式设置
                        bool useLaneWidth = bool.Parse(key.GetValue("radioButtonLaneWidth", "True").ToString());
                        this.radioButtonLaneWidth.Checked = useLaneWidth;
                        this.radioButtonTotalWidth.Checked = !useLaneWidth;
                    }
                    else
                    {
                        // 如果注册表键不存在，设置默认值
                        SetDefaultValues();
                    }
                }
            }
            catch (System.Exception)
            {
                // 如果加载失败，使用默认值
                SetDefaultValues();
            }
        }

        // 设置默认值
        private void SetDefaultValues()
        {
            this.textBox1.Text = "0.2";      // 中心双黄线间距
            this.textBox2.Text = "3.5";      // 单车道宽度
            this.textBox3.Text = "0.5";      // 单黄线宽度
            this.textBox同向车道数.Text = "1";  // 同向车道数量
            this.textBox4.Text = "0.5";      // 斑马线单线条宽度
            this.textBox5.Text = "2.0";      // 人行道宽度
            this.textBox6.Text = "2.5";      // 非机动车道宽度
            this.textBox7.Text = "0.15";     // 车道边线宽度
            this.textBox8.Text = "0.15";     // 车道虚线宽度
            this.textBox9.Text = "6.0";      // 虚线每段长度
            this.textBox10.Text = "5";       // 斑马线条总数
            this.textBox11.Text = "4.0";     // 斑马线单线条长度
            this.textBox12.Text = "3.0";     // 斑马线距停车线距离
            this.textBox13.Text = "0.2";     // 停止线宽度
            this.textBox15.Text = "9.0";     // 虚线间隔

            // 计算模式默认值
            this.radioButtonLaneWidth.Checked = true;      // 默认以单车道宽度计算
            this.radioButtonTotalWidth.Checked = false;
        }

        // 设置计算模式控件
        private void SetupCalculationModeControls()
        {
            // 这里可以添加计算模式相关的事件处理
        }

        // 生成道路标线按钮点击事件
        private void button1_Click(object sender, EventArgs e)
        {
            // 隐藏窗体以避免干扰用户操作
            this.Hide();

            Document doc = AcApp.DocumentManager.MdiActiveDocument;
            if (doc == null)
            {
                MessageBox.Show("没有活动的AutoCAD文档！");
                this.Show();
                return;
            }

            // 使用文档锁定来确保线程安全
            using (doc.LockDocument())
            {
                Database db = doc.Database;
                Editor editor = doc.Editor;

                try
                {
                    // 获取所有参数
                    int laneCount = int.Parse(this.textBox同向车道数.Text);
                    double laneWidth = double.Parse(this.textBox2.Text);
                    double centerLineGap = double.Parse(this.textBox1.Text);
                    double centerLineWidth = double.Parse(this.textBox3.Text);
                    double lineWidth = double.Parse(this.textBox8.Text);
                    double edgeLineWidth = double.Parse(this.textBox7.Text);
                    double bikeLineWidth = double.Parse(this.textBox6.Text);
                    double sidewalkWidth = double.Parse(this.textBox5.Text);
                    double dashLength = double.Parse(this.textBox9.Text);
                    double dashGap = double.Parse(this.textBox15.Text);
                    double zebraWidth = double.Parse(this.textBox4.Text);
                    int zebraCount = int.Parse(this.textBox10.Text);
                    double zebraLineLength = double.Parse(this.textBox11.Text);
                    double zebraDistance = double.Parse(this.textBox12.Text);
                    double stopLineWidth = double.Parse(this.textBox13.Text);

                    // 选择基准线
                    PromptEntityOptions peo = new PromptEntityOptions("\n请选择道路中心基准线: ");
                    peo.SetRejectMessage("\n必须选择曲线对象");
                    peo.AddAllowedClass(typeof(Line), true);
                    peo.AddAllowedClass(typeof(Polyline), true);
                    peo.AddAllowedClass(typeof(Arc), true);
                    peo.AddAllowedClass(typeof(Spline), true);

                    PromptEntityResult per = editor.GetEntity(peo);
                    if (per.Status != PromptStatus.OK)
                    {
                        this.Show();
                        return;
                    }

                    using (Transaction trans = db.TransactionManager.StartTransaction())
                    {
                        try
                        {
                            BlockTable bt = (BlockTable)trans.GetObject(db.BlockTableId, OpenMode.ForRead);
                            BlockTableRecord btr = (BlockTableRecord)trans.GetObject(bt[BlockTableRecord.ModelSpace], OpenMode.ForWrite);

                            Curve baseCurve = (Curve)trans.GetObject(per.ObjectId, OpenMode.ForRead);

                            // 清理缓存以确保性能
                            ClearPerformanceCaches();

                            // 计算需要减去的长度
                            double cutLength = zebraLineLength + zebraDistance + stopLineWidth;

                            // 从基准线两端减去指定长度，得到起始线
                            Curve startLine = CreateStartLine(baseCurve, cutLength, editor);
                            if (startLine == null)
                            {
                                editor.WriteMessage("\n错误：无法创建起始线，基准线可能太短");
                                return;
                            }

                            // 显示起始线
                            short currentColor = GetCurrentColor(editor);
                            startLine.ColorIndex = currentColor;
                            btr.AppendEntity(startLine);
                            trans.AddNewlyCreatedDBObject(startLine, true);

                            // 为单车道生成道路标线
                            if (laneCount == 1)
                            {
                                CreateSingleLaneMarkings(startLine, baseCurve, lineWidth, laneWidth, edgeLineWidth, dashLength, dashGap, btr, trans, editor);

                                // 检查是否需要生成铣底
                                if (this.checkBoxMilling.Checked)
                                {
                                    editor.WriteMessage("\n开始生成单车道铣底...");
                                    CreateSingleLaneMilling(startLine, lineWidth, laneWidth, edgeLineWidth, bikeLineWidth, stopLineWidth, btr, trans, editor);
                                }
                            }

                            editor.WriteMessage("\n道路标线生成完成！");

                            trans.Commit();
                        }
                        catch (Autodesk.AutoCAD.Runtime.Exception ex)
                        {
                            editor.WriteMessage($"\nAutoCAD错误: {ex.Message}\n");
                            trans.Abort();
                        }
                        catch (System.Exception ex)
                        {
                            editor.WriteMessage($"\n系统错误: {ex.Message}\n");
                            trans.Abort();
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n总体错误: {ex.Message}\n");
                }
                finally
                {
                    this.Show(); // 确保窗体重新显示
                }
            }
        }

        // 关闭按钮点击事件
        private void button2_Click(object sender, EventArgs e)
        {
            SaveSettings(); // 保存设置
            this.Hide();
        }

        // 获取当前颜色
        private short GetCurrentColor(Editor editor)
        {
            try
            {
                // 获取当前颜色索引
                object currentColor = Autodesk.AutoCAD.ApplicationServices.Application.GetSystemVariable("CECOLOR");
                if (currentColor != null && short.TryParse(currentColor.ToString(), out short colorIndex))
                {
                    return colorIndex;
                }
                return 7; // 默认白色
            }
            catch
            {
                return 7; // 默认白色
            }
        }

        // 获取铣底颜色
        private short GetMillingColor()
        {
            try
            {
                string selectedColor = this.comboBoxMillingColor.SelectedItem?.ToString() ?? "绿";

                // 将中文颜色名称转换为AutoCAD颜色索引
                switch (selectedColor)
                {
                    case "红": return 1;    // 红色
                    case "蓝": return 5;    // 蓝色
                    case "绿": return 3;    // 绿色
                    case "黄": return 2;    // 黄色
                    case "青": return 4;    // 青色
                    case "洋红": return 6;  // 洋红色
                    default: return 3;     // 默认绿色
                }
            }
            catch
            {
                return 3; // 默认绿色
            }
        }

        // 创建起始线：从基准线两端减去指定长度，保持原始曲线形状
        private Curve CreateStartLine(Curve baseCurve, double cutLength, Editor editor)
        {
            try
            {
                // 获取基准线的总长度
                double totalLength = GetCurveLength(baseCurve);
                editor.WriteMessage($"\n基准线总长度: {totalLength}");

                // 检查是否有足够的长度
                if (totalLength <= 2 * cutLength)
                {
                    editor.WriteMessage($"\n基准线太短，无法减去 {2 * cutLength} 的长度");
                    return null;
                }

                // 使用分割方法保持原始曲线形状
                Point3dCollection splitPoints = new Point3dCollection();

                // 添加起点和终点的分割点
                Point3d startCutPoint = GetPointAtDistance(baseCurve, cutLength);
                Point3d endCutPoint = GetPointAtDistance(baseCurve, totalLength - cutLength);

                splitPoints.Add(startCutPoint);
                splitPoints.Add(endCutPoint);

                // 使用GetSplitCurves方法分割曲线
                DBObjectCollection splitCurves = baseCurve.GetSplitCurves(splitPoints);

                if (splitCurves.Count >= 3)
                {
                    // 返回中间的曲线段（保持原始形状）
                    Curve resultCurve = (Curve)splitCurves[1];
                    editor.WriteMessage($"\n曲线分割成功: 保持原始{baseCurve.GetType().Name}形状");
                    return resultCurve;
                }
                else
                {
                    // 如果分割失败，使用备用方法
                    editor.WriteMessage($"\n曲线分割失败，使用备用方法");
                    return CreateStartLineBackup(baseCurve, cutLength, totalLength, editor);
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建起始线失败: {ex.Message}，使用备用方法");
                return CreateStartLineBackup(baseCurve, cutLength, GetCurveLength(baseCurve), editor);
            }
        }

        // 备用方法：针对不同曲线类型的特殊处理
        private Curve CreateStartLineBackup(Curve baseCurve, double cutLength, double totalLength, Editor editor)
        {
            try
            {
                if (baseCurve is Line)
                {
                    Line line = (Line)baseCurve;
                    Vector3d direction = (line.EndPoint - line.StartPoint).GetNormal();
                    Point3d newStartPoint = line.StartPoint + direction * cutLength;
                    Point3d newEndPoint = line.EndPoint - direction * cutLength;

                    editor.WriteMessage($"\n直线处理: 保持直线形状");
                    return new Line(newStartPoint, newEndPoint);
                }
                else
                {
                    // 对于其他类型，创建直线
                    Point3d startPt = GetPointAtDistance(baseCurve, cutLength);
                    Point3d endPt = GetPointAtDistance(baseCurve, totalLength - cutLength);
                    editor.WriteMessage($"\n使用直线替代: {baseCurve.GetType().Name}");
                    return new Line(startPt, endPt);
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n备用方法失败: {ex.Message}");
                return null;
            }
        }

        // 优化的曲线长度计算 - 添加缓存
        private static Dictionary<ObjectId, double> _lengthCache = new Dictionary<ObjectId, double>();

        private double GetCurveLength(Curve curve)
        {
            // 尝试从缓存获取
            if (curve.ObjectId != ObjectId.Null && _lengthCache.ContainsKey(curve.ObjectId))
            {
                return _lengthCache[curve.ObjectId];
            }

            double length;

            if (curve is Line)
            {
                Line line = (Line)curve;
                length = line.StartPoint.DistanceTo(line.EndPoint);
            }
            else if (curve is Polyline)
            {
                Polyline pline = (Polyline)curve;
                length = pline.Length;
            }
            else if (curve is Arc)
            {
                Arc arc = (Arc)curve;
                length = arc.Length;
            }
            else if (curve is Spline)
            {
                Spline spline = (Spline)curve;
                try
                {
                    length = spline.GetDistanceAtParameter(spline.EndParam);
                }
                catch
                {
                    length = spline.StartPoint.DistanceTo(spline.EndPoint);
                }
            }
            else
            {
                length = curve.StartPoint.DistanceTo(curve.EndPoint);
            }

            // 缓存结果
            if (curve.ObjectId != ObjectId.Null)
            {
                _lengthCache[curve.ObjectId] = length;
            }

            return length;
        }

        // 优化的点计算方法 - 减少重复的长度计算
        private Point3d GetPointAtDistance(Curve curve, double distance)
        {
            if (curve is Line)
            {
                Line line = (Line)curve;
                Vector3d direction = (line.EndPoint - line.StartPoint);
                double totalLength = direction.Length;
                if (totalLength > 0)
                {
                    direction = direction / totalLength;
                    return line.StartPoint + direction * distance;
                }
                return line.StartPoint;
            }
            else if (curve is Polyline)
            {
                Polyline pline = (Polyline)curve;
                try
                {
                    return pline.GetPointAtDist(distance);
                }
                catch
                {
                    return LinearInterpolatePoint(curve, distance);
                }
            }
            else if (curve is Arc)
            {
                Arc arc = (Arc)curve;
                try
                {
                    return arc.GetPointAtDist(distance);
                }
                catch
                {
                    return LinearInterpolatePoint(curve, distance);
                }
            }
            else if (curve is Spline)
            {
                Spline spline = (Spline)curve;
                try
                {
                    return spline.GetPointAtDist(distance);
                }
                catch
                {
                    return LinearInterpolatePoint(curve, distance);
                }
            }
            else
            {
                return LinearInterpolatePoint(curve, distance);
            }
        }

        // 线性插值计算点位置
        private Point3d LinearInterpolatePoint(Curve curve, double distance)
        {
            double totalLength = GetCurveLength(curve);
            if (totalLength <= 0) return curve.StartPoint;

            double ratio = Math.Min(distance / totalLength, 1.0);
            Vector3d vector = curve.EndPoint - curve.StartPoint;
            return curve.StartPoint + vector * ratio;
        }

        // 优化的单车道标线生成方法 - 包含边线封口和虚线引导线
        private void CreateSingleLaneMarkings(Curve startLine, Curve originalBaseline, double lineWidth, double laneWidth, double edgeLineWidth,
                                            double dashLength, double dashGap, BlockTableRecord btr, Transaction trans, Editor editor)
        {
            try
            {
                // 预计算常用值
                double offset1 = (lineWidth / 2.0) + laneWidth;
                double offset2 = offset1 + edgeLineWidth;
                short currentColor = GetCurrentColor(editor);

                // 批量计算所有偏移曲线 - 一次性完成所有偏移计算
                var laneOffsets = GetCorrectLeftRightOffsets(startLine, offset1, editor, false);
                var edgeOffsets = GetCorrectLeftRightOffsets(startLine, offset2, editor, false);

                // 收集所有要添加的实体
                List<Entity> entitiesToAdd = new List<Entity>();

                // 第一步：创建带引导线的虚线效果
                CreateDashedStartLineWithGuides(startLine, dashLength, dashGap, lineWidth, currentColor, entitiesToAdd, editor);

                // 第二步：添加车道边界线（保持原有逻辑）
                if (laneOffsets.rightCurve != null)
                {
                    laneOffsets.rightCurve.ColorIndex = currentColor;
                    entitiesToAdd.Add(laneOffsets.rightCurve);
                }

                if (laneOffsets.leftCurve != null)
                {
                    laneOffsets.leftCurve.ColorIndex = currentColor;
                    entitiesToAdd.Add(laneOffsets.leftCurve);
                }

                // 第三步：新的车道边线算法（参考车道虚线的正确偏移方式）
                // 第三步-第一部分：单车道左侧边线
                CreateLeftSideLaneEdges(startLine, lineWidth, laneWidth, edgeLineWidth, currentColor, entitiesToAdd, editor);

                // 第三步-第二部分：单车道右侧边线
                CreateRightSideLaneEdges(startLine, lineWidth, laneWidth, edgeLineWidth, currentColor, entitiesToAdd, editor);

                // 注意：禁止用新生成的线条代替单车道左侧内边线和右侧内边线
                // 新算法生成的是额外的边线，不替换现有的车道边界线

                // 第四步：创建停止线（修复位置和方向）
                double stopLineWidth = GetDoubleValue(this.textBox13, 0.2);
                CreateStopLinesFixed(startLine, laneOffsets.leftCurve, laneOffsets.rightCurve,
                                   lineWidth, laneWidth, edgeLineWidth,
                                   GetDoubleValue(this.textBox6, 2.5), stopLineWidth,
                                   currentColor, entitiesToAdd, editor);

                // 注意：边线封口已在各自的方法中完成，无需额外处理

                // 批量添加所有实体到数据库 - 大幅提升性能
                foreach (Entity entity in entitiesToAdd)
                {
                    btr.AppendEntity(entity);
                    trans.AddNewlyCreatedDBObject(entity, true);
                }

                editor.WriteMessage($"\n道路标线生成完成，共创建 {entitiesToAdd.Count} 个实体");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n单车道标线生成失败: {ex.Message}");
            }
        }

        // 严格优先级虚线生成算法 - 间隔和中间虚线段长度绝对不变
        private void CreateDashedStartLineWithGuides(Curve startLine, double dashLength, double dashGap, double lineWidth,
                                                   short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                double totalLength = GetCurveLength(startLine);
                double offsetDistance = lineWidth * 0.5;

                editor.WriteMessage($"\n=== 严格优先级虚线生成算法 ===");
                editor.WriteMessage($"\n基准线总长度: {totalLength:F3}");
                editor.WriteMessage($"\n用户设定虚线段长度: {dashLength:F3}");
                editor.WriteMessage($"\n用户设定虚线间隔: {dashGap:F3}");

                // 长虚线长度范围 - 修复：最大长度应包含2个间隔
                double minLongDash = dashLength * 2 + dashGap * 1;      // 最小：2段虚线 + 1个间隔
                double maxLongDash = dashLength * 3 + dashGap * 2;      // 最大：3段虚线 + 2个间隔
                editor.WriteMessage($"\n长虚线长度范围: {minLongDash:F3} - {maxLongDash:F3}");

                // 计算最优分割方案
                var optimalLayout = CalculateStrictPriorityLayout(totalLength, dashLength, dashGap, minLongDash, maxLongDash, editor);

                if (optimalLayout == null)
                {
                    editor.WriteMessage("\n无法找到有效的分割方案，创建实线标记");
                    CreateCurvedSolidLine(startLine, offsetDistance, currentColor, entitiesToAdd, editor);
                    return;
                }

                // 根据计算结果创建虚线
                CreateDashSegmentsFromLayout(startLine, optimalLayout, offsetDistance, currentColor, entitiesToAdd, editor);

                editor.WriteMessage($"\n严格优先级虚线生成完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n严格优先级虚线生成失败: {ex.Message}");
            }
        }

        // 通过参数创建样条曲线段
        private Curve CreateSplineByParameters(Spline originalSpline, double startParam, double endParam, Editor editor)
        {
            try
            {
                // 获取样条曲线的控制点和参数
                int numSamples = Math.Max(10, (int)((endParam - startParam) * 20)); // 根据参数范围确定采样点数
                Point3dCollection points = new Point3dCollection();

                for (int i = 0; i <= numSamples; i++)
                {
                    double param = startParam + (endParam - startParam) * i / numSamples;
                    Point3d point = originalSpline.GetPointAtParameter(param);
                    points.Add(point);
                }

                if (points.Count >= 2)
                {
                    // 创建新的样条曲线
                    Spline newSpline = new Spline(points, 3, 0.0); // 3次样条，容差0.0
                    editor.WriteMessage($"\n通过参数创建样条曲线成功，采样点数: {points.Count}");
                    return newSpline;
                }

                return null;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n参数化样条曲线创建失败: {ex.Message}");
                return null;
            }
        }

        // 尝试分割曲线方法
        private Curve TrySplitCurveMethod(Curve baseCurve, double startDist, double endDist, Editor editor)
        {
            try
            {
                Point3d startPoint = GetPointAtDistance(baseCurve, startDist);
                Point3d endPoint = GetPointAtDistance(baseCurve, endDist);

                Point3dCollection splitPoints = new Point3dCollection();
                splitPoints.Add(startPoint);
                splitPoints.Add(endPoint);

                DBObjectCollection splitCurves = baseCurve.GetSplitCurves(splitPoints);
                if (splitCurves.Count >= 3)
                {
                    editor.WriteMessage($"\n通用分割方法成功");
                    return (Curve)splitCurves[1];
                }
                else
                {
                    editor.WriteMessage($"\n通用分割方法失败，分割结果数量: {splitCurves.Count}");
                    return new Line(startPoint, endPoint);
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n通用分割方法异常: {ex.Message}");
                Point3d startPoint = GetPointAtDistance(baseCurve, startDist);
                Point3d endPoint = GetPointAtDistance(baseCurve, endDist);
                return new Line(startPoint, endPoint);
            }
        }

        // 创建圆弧段
        private Curve CreateArcSegment(Arc arc, double startDist, double endDist, Editor editor)
        {
            try
            {
                double totalLength = GetCurveLength(arc);
                if (totalLength <= 0) return null;

                // 计算角度参数
                double startAngle = arc.StartAngle + (arc.EndAngle - arc.StartAngle) * (startDist / totalLength);
                double endAngle = arc.StartAngle + (arc.EndAngle - arc.StartAngle) * (endDist / totalLength);

                // 创建新的圆弧段
                Arc newArc = new Arc(arc.Center, arc.Radius, startAngle, endAngle);
                editor.WriteMessage($"\n圆弧段创建成功");
                return newArc;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建圆弧段失败: {ex.Message}");
                Point3d startPoint = GetPointAtDistance(arc, startDist);
                Point3d endPoint = GetPointAtDistance(arc, endDist);
                return new Line(startPoint, endPoint);
            }
        }

        // 创建多段线段（保持原始特征）
        private Curve CreatePolylineSegment(Polyline polyline, double startDist, double endDist, Editor editor)
        {
            try
            {
                // 获取起始和结束点
                Point3d startPoint = GetPointAtDistance(polyline, startDist);
                Point3d endPoint = GetPointAtDistance(polyline, endDist);

                // 优先使用分割方法保持原始几何特征
                try
                {
                    Point3dCollection splitPoints = new Point3dCollection();
                    splitPoints.Add(startPoint);
                    splitPoints.Add(endPoint);

                    DBObjectCollection splitCurves = polyline.GetSplitCurves(splitPoints);
                    if (splitCurves.Count >= 3)
                    {
                        Curve middleSegment = (Curve)splitCurves[1];
                        if (middleSegment is Polyline resultPolyline)
                        {
                            editor.WriteMessage($"\n多段线分割成功，顶点数: {resultPolyline.NumberOfVertices}");
                            return resultPolyline;
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n多段线分割失败: {ex.Message}");
                }

                // 备用方法：创建简化的两点多段线
                try
                {
                    Polyline newPolyline = new Polyline();
                    newPolyline.AddVertexAt(0, new Point2d(startPoint.X, startPoint.Y), 0, 0, 0);
                    newPolyline.AddVertexAt(1, new Point2d(endPoint.X, endPoint.Y), 0, 0, 0);
                    editor.WriteMessage($"\n创建简化多段线");
                    return newPolyline;
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n创建简化多段线失败: {ex.Message}");
                }

                // 最后备用方法：直线段
                editor.WriteMessage($"\n多段线处理失败，使用直线段");
                return new Line(startPoint, endPoint);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建多段线段失败: {ex.Message}");
                Point3d startPoint = GetPointAtDistance(polyline, startDist);
                Point3d endPoint = GetPointAtDistance(polyline, endDist);
                return new Line(startPoint, endPoint);
            }
        }

        // 创建保持曲线形状的引导线
        private void CreateCurvedGuideLine(Curve baseLine, double startDist, double endDist, double offsetDistance,
                                         short currentColor, List<Entity> entitiesToAdd, string description, Editor editor)
        {
            try
            {
                // 创建引导线段，保持原始曲线形状
                Curve guideSegment = CreateCurveSegment(baseLine, startDist, endDist, editor);
                if (guideSegment != null)
                {
                    // 直接偏移曲线，不替换原始形状
                    CreateCurvedSolidLine(guideSegment, offsetDistance, currentColor, entitiesToAdd, editor);
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建曲线引导线失败: {ex.Message}");
            }
        }

        // 创建保持曲线形状的实线
        private void CreateCurvedSolidLine(Curve centerLine, double offsetDistance, short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n    创建实线段: 类型={centerLine.GetType().Name}, 偏移={offsetDistance:F3}");

                // 直接偏移曲线，保持原始形状
                var leftRightOffsets = GetCorrectLeftRightOffsets(centerLine, offsetDistance, editor, false);

                if (leftRightOffsets.leftCurve != null && leftRightOffsets.rightCurve != null)
                {
                    leftRightOffsets.leftCurve.ColorIndex = currentColor;
                    leftRightOffsets.rightCurve.ColorIndex = currentColor;
                    entitiesToAdd.Add(leftRightOffsets.leftCurve);
                    entitiesToAdd.Add(leftRightOffsets.rightCurve);

                    // 创建端点连接线
                    Line startConnection = new Line(leftRightOffsets.leftCurve.StartPoint, leftRightOffsets.rightCurve.StartPoint);
                    Line endConnection = new Line(leftRightOffsets.leftCurve.EndPoint, leftRightOffsets.rightCurve.EndPoint);

                    startConnection.ColorIndex = currentColor;
                    endConnection.ColorIndex = currentColor;
                    entitiesToAdd.Add(startConnection);
                    entitiesToAdd.Add(endConnection);

                    editor.WriteMessage($"\n    实线段创建成功: 添加了4个实体（左线、右线、起始连接、结束连接）");
                }
                else
                {
                    editor.WriteMessage($"\n    偏移失败: 左曲线={leftRightOffsets.leftCurve != null}, 右曲线={leftRightOffsets.rightCurve != null}");
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建曲线实线失败: {ex.Message}");
            }
        }

        // 创建曲线段，严格保持原始曲线特征
        private Curve CreateCurveSegment(Curve baseCurve, double startDist, double endDist, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n创建曲线段: 类型={baseCurve.GetType().Name}, 距离={startDist:F3}-{endDist:F3}");

                if (baseCurve is Line)
                {
                    // 对于直线，创建线段
                    Point3d startPoint = GetPointAtDistance(baseCurve, startDist);
                    Point3d endPoint = GetPointAtDistance(baseCurve, endDist);
                    return new Line(startPoint, endPoint);
                }
                else if (baseCurve is Spline spline)
                {
                    // 对于样条曲线，使用参数化方法保持曲线特征
                    return CreateSplineSegment(spline, startDist, endDist, editor);
                }
                else if (baseCurve is Polyline polyline)
                {
                    // 对于多段线，使用专门的方法
                    return CreatePolylineSegment(polyline, startDist, endDist, editor);
                }
                else if (baseCurve is Arc arc)
                {
                    // 对于圆弧，使用角度参数化方法
                    return CreateArcSegment(arc, startDist, endDist, editor);
                }
                else
                {
                    // 对于其他曲线类型，尝试分割方法
                    return TrySplitCurveMethod(baseCurve, startDist, endDist, editor);
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建曲线段失败: {ex.Message}");
                // 最后的备用方法：创建直线段
                Point3d startPoint = GetPointAtDistance(baseCurve, startDist);
                Point3d endPoint = GetPointAtDistance(baseCurve, endDist);
                editor.WriteMessage($"\n使用直线段作为备用方案");
                return new Line(startPoint, endPoint);
            }
        }

        // 创建样条曲线段（保持曲线特征）
        private Curve CreateSplineSegment(Spline spline, double startDist, double endDist, Editor editor)
        {
            try
            {
                double totalLength = GetCurveLength(spline);
                if (totalLength <= 0) return null;

                // 计算参数范围
                double startParam = spline.StartParam + (spline.EndParam - spline.StartParam) * (startDist / totalLength);
                double endParam = spline.StartParam + (spline.EndParam - spline.StartParam) * (endDist / totalLength);

                editor.WriteMessage($"\n样条曲线段参数: {startParam:F6} - {endParam:F6}");

                // 尝试使用GetSplitCurves方法
                try
                {
                    Point3d startPoint = GetPointAtDistance(spline, startDist);
                    Point3d endPoint = GetPointAtDistance(spline, endDist);

                    Point3dCollection splitPoints = new Point3dCollection();
                    splitPoints.Add(startPoint);
                    splitPoints.Add(endPoint);

                    DBObjectCollection splitCurves = spline.GetSplitCurves(splitPoints);
                    if (splitCurves.Count >= 3)
                    {
                        Curve resultCurve = (Curve)splitCurves[1];
                        editor.WriteMessage($"\n样条曲线分割成功，结果类型: {resultCurve.GetType().Name}");
                        return resultCurve;
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n样条曲线分割失败: {ex.Message}");
                }

                // 备用方法：创建新的样条曲线段
                try
                {
                    return CreateSplineByParameters(spline, startParam, endParam, editor);
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n参数化样条曲线创建失败: {ex.Message}");
                }

                // 最后备用：直线段
                Point3d startPt = GetPointAtDistance(spline, startDist);
                Point3d endPt = GetPointAtDistance(spline, endDist);
                editor.WriteMessage($"\n样条曲线处理失败，使用直线段");
                return new Line(startPt, endPt);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建样条曲线段失败: {ex.Message}");
                return null;
            }
        }



        // 稳定的严格优先级布局计算 - 间隔和中间虚线段绝对不变
        private DashLayout CalculateStrictPriorityLayout(double totalLength, double dashLength, double dashGap,
                                                        double minLongDash, double maxLongDash, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 稳定的严格优先级布局计算 ===");

                // 放宽最小长度要求，提高稳定性
                double absoluteMinLength = 2 * dashLength + 3 * dashGap; // 更实际的最小长度
                if (totalLength < absoluteMinLength)
                {
                    editor.WriteMessage($"\n总长度过短: 需要至少{absoluteMinLength:F3}, 实际{totalLength:F3}");
                    // 即使长度不足，也尝试创建简化的虚线
                    return CreateFallbackLayout(totalLength, dashLength, dashGap, editor);
                }

                // 尝试不同的中间虚线段数量，找到最优方案
                DashLayout bestLayout = null;
                double bestScore = double.MaxValue;

                // 扩大搜索范围，提高成功率
                int maxMiddleDashes = Math.Max(0, (int)((totalLength - 2 * dashLength - 2 * dashGap) / (dashLength + dashGap)));

                editor.WriteMessage($"\n搜索范围: 0 到 {maxMiddleDashes} 个中间虚线段");

                for (int middleDashCount = 0; middleDashCount <= maxMiddleDashes; middleDashCount++)
                {
                    // 计算固定部分的总长度（严格按用户输入）
                    double fixedMiddleLength = middleDashCount * dashLength + (middleDashCount + 1) * dashGap;

                    // 剩余长度分配给两个长虚线
                    double remainingForLongDashes = totalLength - fixedMiddleLength;
                    double averageLongDash = remainingForLongDashes / 2.0;

                    editor.WriteMessage($"\n  尝试 {middleDashCount} 个中间段: 平均长虚线={averageLongDash:F3}");

                    // 严格按照用户要求的长虚线长度约束
                    // 最小长度：dashLength × 2 + gap × 1
                    // 最大长度：dashLength × 3 + gap × 2
                    if (averageLongDash >= minLongDash && averageLongDash <= maxLongDash)
                    {
                        // 计算评分（越接近理想长度越好）
                        double idealLongDash = (minLongDash + maxLongDash) / 2.0;
                        double score = Math.Abs(averageLongDash - idealLongDash);

                        editor.WriteMessage($"\n    有效方案: 评分={score:F3}");

                        if (score < bestScore)
                        {
                            bestScore = score;
                            bestLayout = new DashLayout
                            {
                                LeftLongDashLength = averageLongDash,
                                RightLongDashLength = averageLongDash,
                                MiddleDashCount = middleDashCount,
                                MiddleDashLength = dashLength,
                                GapLength = dashGap
                            };
                        }
                    }
                }

                if (bestLayout != null)
                {
                    editor.WriteMessage($"\n最优布局方案:");
                    editor.WriteMessage($"\n  左侧长虚线: {bestLayout.LeftLongDashLength:F3}");
                    editor.WriteMessage($"\n  右侧长虚线: {bestLayout.RightLongDashLength:F3}");
                    editor.WriteMessage($"\n  中间虚线段数: {bestLayout.MiddleDashCount}");
                    editor.WriteMessage($"\n  中间虚线段长度: {bestLayout.MiddleDashLength:F3} (用户设定)");
                    editor.WriteMessage($"\n  间隔长度: {bestLayout.GapLength:F3} (用户设定)");

                    // 验证总长度
                    double calculatedTotal = bestLayout.LeftLongDashLength + bestLayout.RightLongDashLength +
                                           bestLayout.MiddleDashCount * bestLayout.MiddleDashLength +
                                           (bestLayout.MiddleDashCount + 1) * bestLayout.GapLength;
                    editor.WriteMessage($"\n  计算总长度: {calculatedTotal:F3}");
                    editor.WriteMessage($"\n  实际总长度: {totalLength:F3}");
                    editor.WriteMessage($"\n  长度差异: {Math.Abs(calculatedTotal - totalLength):F6}");
                }
                else
                {
                    editor.WriteMessage($"\n标准算法无法找到方案，尝试备用算法");
                    return CreateFallbackLayout(totalLength, dashLength, dashGap, editor);
                }

                return bestLayout;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n布局计算失败: {ex.Message}，使用备用算法");
                return CreateFallbackLayout(totalLength, dashLength, dashGap, editor);
            }
        }

        // 备用布局算法 - 确保在任何情况下都能生成虚线
        private DashLayout CreateFallbackLayout(double totalLength, double dashLength, double dashGap, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 备用布局算法 ===");

                // 最简单的情况：只有两个长虚线和一个间隔
                if (totalLength >= 2 * dashLength + dashGap)
                {
                    double remainingLength = totalLength - dashGap;
                    double longDashLength = remainingLength / 2.0;

                    editor.WriteMessage($"\n简化布局: 两个长虚线 + 一个间隔");
                    editor.WriteMessage($"\n  长虚线长度: {longDashLength:F3}");
                    editor.WriteMessage($"\n  间隔长度: {dashGap:F3}");

                    return new DashLayout
                    {
                        LeftLongDashLength = longDashLength,
                        RightLongDashLength = longDashLength,
                        MiddleDashCount = 0,
                        MiddleDashLength = dashLength,
                        GapLength = dashGap
                    };
                }

                // 极端情况：总长度很短，创建单一虚线段
                editor.WriteMessage($"\n极简布局: 单一虚线段");
                return new DashLayout
                {
                    LeftLongDashLength = totalLength,
                    RightLongDashLength = 0,
                    MiddleDashCount = 0,
                    MiddleDashLength = dashLength,
                    GapLength = dashGap
                };
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n备用布局失败: {ex.Message}");
                return null;
            }
        }

        // 虚线布局数据结构
        private class DashLayout
        {
            public double LeftLongDashLength { get; set; }
            public double RightLongDashLength { get; set; }
            public int MiddleDashCount { get; set; }
            public double MiddleDashLength { get; set; }
            public double GapLength { get; set; }
        }

        // 稳定的根据布局创建虚线段
        private void CreateDashSegmentsFromLayout(Curve startLine, DashLayout layout, double offsetDistance,
                                                 short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 稳定的虚线段创建 ===");
                double currentPos = 0;
                int segmentCount = 0;

                // 处理特殊情况：单一虚线段
                if (layout.RightLongDashLength == 0)
                {
                    editor.WriteMessage($"\n创建单一虚线段: {currentPos:F3} - {layout.LeftLongDashLength:F3}");
                    CreateSingleDashSegment(startLine, currentPos, layout.LeftLongDashLength,
                                          offsetDistance, currentColor, entitiesToAdd, editor);
                    editor.WriteMessage($"\n单一虚线段创建完成");
                    return;
                }

                // 1. 创建左侧长虚线（确保长度有效）
                if (layout.LeftLongDashLength > 0.001) // 避免创建过短的段
                {
                    editor.WriteMessage($"\n创建左侧长虚线: {currentPos:F3} - {currentPos + layout.LeftLongDashLength:F3}");
                    CreateSingleDashSegment(startLine, currentPos, currentPos + layout.LeftLongDashLength,
                                          offsetDistance, currentColor, entitiesToAdd, editor);
                    currentPos += layout.LeftLongDashLength;
                    segmentCount++;
                }

                // 2. 创建中间的虚线段和间隔
                for (int i = 0; i <= layout.MiddleDashCount; i++)
                {
                    // 添加间隔（严格等于用户设定）
                    if (layout.GapLength > 0.001) // 确保间隔有效
                    {
                        double gapStart = currentPos;
                        double gapEnd = currentPos + layout.GapLength;
                        editor.WriteMessage($"\n间隔 {i + 1}: {gapStart:F3} - {gapEnd:F3} (长度: {layout.GapLength:F3})");
                        currentPos = gapEnd;
                    }

                    // 添加中间虚线段（除了最后一次循环）
                    if (i < layout.MiddleDashCount && layout.MiddleDashLength > 0.001)
                    {
                        double dashStart = currentPos;
                        double dashEnd = currentPos + layout.MiddleDashLength;
                        editor.WriteMessage($"\n中间虚线段 {i + 1}: {dashStart:F3} - {dashEnd:F3} (长度: {layout.MiddleDashLength:F3})");
                        CreateSingleDashSegment(startLine, dashStart, dashEnd, offsetDistance, currentColor, entitiesToAdd, editor);
                        currentPos = dashEnd;
                        segmentCount++;
                    }
                }

                // 3. 创建右侧长虚线（确保长度有效）
                if (layout.RightLongDashLength > 0.001)
                {
                    double rightDashStart = currentPos;
                    double rightDashEnd = currentPos + layout.RightLongDashLength;
                    editor.WriteMessage($"\n创建右侧长虚线: {rightDashStart:F3} - {rightDashEnd:F3}");
                    CreateSingleDashSegment(startLine, rightDashStart, rightDashEnd, offsetDistance, currentColor, entitiesToAdd, editor);
                    segmentCount++;
                }

                editor.WriteMessage($"\n稳定虚线段创建完成，共创建 {segmentCount} 个虚线段");

                // 验证间隔长度的严格性
                VerifyGapLengths(layout, editor);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建虚线段失败: {ex.Message}");
                editor.WriteMessage($"\n错误详情: {ex.StackTrace}");

                // 即使出错也尝试创建简单的虚线
                try
                {
                    editor.WriteMessage($"\n尝试创建简单虚线作为备用");
                    CreateSimpleDashedLine(startLine, layout.MiddleDashLength, layout.GapLength, offsetDistance, currentColor, entitiesToAdd, editor);
                }
                catch
                {
                    editor.WriteMessage($"\n备用虚线创建也失败");
                }
            }
        }

        // 简单虚线创建方法 - 最后的备用方案
        private void CreateSimpleDashedLine(Curve startLine, double dashLength, double dashGap, double offsetDistance,
                                          short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 简单虚线备用方案 ===");
                double totalLength = GetCurveLength(startLine);
                double currentPos = 0;
                int segmentCount = 0;

                // 简单的等间距虚线分布
                while (currentPos + dashLength <= totalLength)
                {
                    // 创建虚线段
                    CreateSingleDashSegment(startLine, currentPos, currentPos + dashLength,
                                          offsetDistance, currentColor, entitiesToAdd, editor);
                    currentPos += dashLength;
                    segmentCount++;

                    // 跳过间隔
                    currentPos += dashGap;

                    // 防止无限循环
                    if (segmentCount > 100) break;
                }

                editor.WriteMessage($"\n简单虚线创建完成，共 {segmentCount} 段");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n简单虚线创建失败: {ex.Message}");
            }
        }

        // 稳定的创建单个虚线段
        private void CreateSingleDashSegment(Curve baseLine, double startDist, double endDist, double offsetDistance,
                                           short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                // 验证输入参数
                if (endDist <= startDist)
                {
                    editor.WriteMessage($"\n跳过无效虚线段: 起始{startDist:F3} >= 结束{endDist:F3}");
                    return;
                }

                double segmentLength = endDist - startDist;
                if (segmentLength < 0.001) // 过短的段
                {
                    editor.WriteMessage($"\n跳过过短虚线段: 长度{segmentLength:F3}");
                    return;
                }

                // 创建虚线段的基准线
                Curve segmentCurve = CreateCurveSegment(baseLine, startDist, endDist, editor);
                if (segmentCurve != null)
                {
                    // 创建实线段（包含偏移和连接线）
                    CreateCurvedSolidLine(segmentCurve, offsetDistance, currentColor, entitiesToAdd, editor);
                    editor.WriteMessage($"\n  虚线段创建成功: {startDist:F3}-{endDist:F3} (长度{segmentLength:F3})");
                }
                else
                {
                    editor.WriteMessage($"\n  虚线段基准线创建失败: {startDist:F3}-{endDist:F3}");

                    // 备用方案：直接创建简单矩形
                    CreateSimpleRectangleSegment(baseLine, startDist, endDist, offsetDistance, currentColor, entitiesToAdd, editor);
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建单个虚线段失败: {ex.Message}");

                // 最后的备用方案
                try
                {
                    CreateSimpleRectangleSegment(baseLine, startDist, endDist, offsetDistance, currentColor, entitiesToAdd, editor);
                }
                catch
                {
                    editor.WriteMessage($"\n备用方案也失败");
                }
            }
        }

        // 简单矩形虚线段创建 - 最稳定的备用方案
        private void CreateSimpleRectangleSegment(Curve baseLine, double startDist, double endDist, double offsetDistance,
                                                 short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                Point3d startPoint = GetPointAtDistance(baseLine, startDist);
                Point3d endPoint = GetPointAtDistance(baseLine, endDist);

                if (startPoint.DistanceTo(endPoint) < 0.001) return;

                // 创建简单的矩形虚线段
                Vector3d direction = (endPoint - startPoint).GetNormal();
                Vector3d normal = new Vector3d(-direction.Y, direction.X, 0).GetNormal();

                Point3d p1 = startPoint + normal * offsetDistance;
                Point3d p2 = endPoint + normal * offsetDistance;
                Point3d p3 = endPoint - normal * offsetDistance;
                Point3d p4 = startPoint - normal * offsetDistance;

                // 创建矩形的四条边
                Line[] rectangleLines = new Line[]
                {
                    new Line(p1, p2),
                    new Line(p2, p3),
                    new Line(p3, p4),
                    new Line(p4, p1)
                };

                foreach (Line line in rectangleLines)
                {
                    line.ColorIndex = currentColor;
                    entitiesToAdd.Add(line);
                }

                editor.WriteMessage($"\n  简单矩形段创建成功");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n简单矩形段创建失败: {ex.Message}");
            }
        }

        // 验证间隔长度的严格性
        private void VerifyGapLengths(DashLayout layout, Editor editor)
        {
            editor.WriteMessage($"\n=== 间隔长度验证 ===");
            editor.WriteMessage($"\n用户设定间隔长度: {layout.GapLength:F3}");
            editor.WriteMessage($"\n间隔数量: {layout.MiddleDashCount + 1}");
            editor.WriteMessage($"\n所有间隔长度均严格等于用户设定值");

            // 验证中间虚线段长度
            if (layout.MiddleDashCount > 0)
            {
                editor.WriteMessage($"\n用户设定虚线段长度: {layout.MiddleDashLength:F3}");
                editor.WriteMessage($"\n中间虚线段数量: {layout.MiddleDashCount}");
                editor.WriteMessage($"\n所有中间虚线段长度均严格等于用户设定值");
            }
        }



        // 创建实线矩形（从整条线）
        private void CreateSolidRectangle(Curve centerLine, double offsetDistance, short currentColor, List<Entity> entitiesToAdd)
        {
            try
            {
                // 计算线段方向和法向量
                Vector3d direction = (centerLine.EndPoint - centerLine.StartPoint).GetNormal();
                Vector3d normal = new Vector3d(-direction.Y, direction.X, 0).GetNormal();

                // 直接计算四个角点
                Point3d p1 = centerLine.StartPoint + normal * offsetDistance;
                Point3d p2 = centerLine.EndPoint + normal * offsetDistance;
                Point3d p3 = centerLine.EndPoint - normal * offsetDistance;
                Point3d p4 = centerLine.StartPoint - normal * offsetDistance;

                // 创建矩形的四条边
                Line[] rectangleLines = new Line[]
                {
                    new Line(p1, p2),  // 上边
                    new Line(p2, p3),  // 右边
                    new Line(p3, p4),  // 下边
                    new Line(p4, p1)   // 左边
                };

                // 设置颜色并添加到列表
                foreach (Line line in rectangleLines)
                {
                    line.ColorIndex = currentColor;
                    entitiesToAdd.Add(line);
                }
            }
            catch (System.Exception ex)
            {
                // 静默处理错误
            }
        }

        // 创建实线矩形（从线段）
        private void CreateSolidRectangleFromLine(Line centerSegment, double offsetDistance, short currentColor, List<Entity> entitiesToAdd)
        {
            try
            {
                // 计算线段方向和法向量
                Vector3d direction = (centerSegment.EndPoint - centerSegment.StartPoint).GetNormal();
                Vector3d normal = new Vector3d(-direction.Y, direction.X, 0).GetNormal();

                // 直接计算四个角点
                Point3d p1 = centerSegment.StartPoint + normal * offsetDistance;
                Point3d p2 = centerSegment.EndPoint + normal * offsetDistance;
                Point3d p3 = centerSegment.EndPoint - normal * offsetDistance;
                Point3d p4 = centerSegment.StartPoint - normal * offsetDistance;

                // 创建矩形的四条边
                Line[] rectangleLines = new Line[]
                {
                    new Line(p1, p2),  // 上边
                    new Line(p2, p3),  // 右边
                    new Line(p3, p4),  // 下边
                    new Line(p4, p1)   // 左边
                };

                // 设置颜色并添加到列表
                foreach (Line line in rectangleLines)
                {
                    line.ColorIndex = currentColor;
                    entitiesToAdd.Add(line);
                }
            }
            catch (System.Exception ex)
            {
                // 静默处理错误
            }
        }

        // 优化的虚线创建方法 - 批量处理，减少重复计算
        private void CreateDashedStartLineOptimized(Curve startLine, double dashLength, double dashGap, double lineWidth,
                                                  short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                double totalLength = GetCurveLength(startLine);
                double offsetDistance = lineWidth * 0.5;

                // 预计算所有分割点
                List<double> splitPoints = CalculateSplitPoints(totalLength, dashLength, dashGap, editor);

                // 批量创建虚线段
                for (int i = 0; i < splitPoints.Count - 1; i += 2)
                {
                    double startDist = splitPoints[i];
                    double endDist = splitPoints[i + 1];

                    // 创建中心线段
                    Point3d startPoint = GetPointAtDistance(startLine, startDist);
                    Point3d endPoint = GetPointAtDistance(startLine, endDist);
                    Line centerSegment = new Line(startPoint, endPoint);

                    // 快速创建虚线矩形 - 避免重复的偏移计算
                    CreateDashedRectangleOptimized(centerSegment, offsetDistance, currentColor, entitiesToAdd);
                }

                editor.WriteMessage($"\n虚线创建完成，共 {splitPoints.Count / 2} 段");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建虚线失败: {ex.Message}");
            }
        }

        // 优化的虚线矩形创建 - 直接计算，避免AutoCAD偏移API
        private void CreateDashedRectangleOptimized(Line centerSegment, double offsetDistance, short currentColor, List<Entity> entitiesToAdd)
        {
            try
            {
                // 计算线段方向和法向量
                Vector3d direction = (centerSegment.EndPoint - centerSegment.StartPoint).GetNormal();
                Vector3d normal = new Vector3d(-direction.Y, direction.X, 0).GetNormal();

                // 直接计算四个角点
                Point3d p1 = centerSegment.StartPoint + normal * offsetDistance;
                Point3d p2 = centerSegment.EndPoint + normal * offsetDistance;
                Point3d p3 = centerSegment.EndPoint - normal * offsetDistance;
                Point3d p4 = centerSegment.StartPoint - normal * offsetDistance;

                // 创建矩形的四条边
                Line[] rectangleLines = new Line[]
                {
                    new Line(p1, p2),  // 上边
                    new Line(p2, p3),  // 右边
                    new Line(p3, p4),  // 下边
                    new Line(p4, p1)   // 左边
                };

                // 设置颜色并添加到列表
                foreach (Line line in rectangleLines)
                {
                    line.ColorIndex = currentColor;
                    entitiesToAdd.Add(line);
                }
            }
            catch (System.Exception ex)
            {
                // 静默处理错误，避免影响整体性能
            }
        }

        // 创建边线封口连接线 - 解决边线封口问题
        private void CreateEdgeClosureLines(Curve leftLaneLine, Curve leftEdgeLine,
                                          Curve rightLaneLine, Curve rightEdgeLine,
                                          short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                if (leftLaneLine == null || leftEdgeLine == null || rightLaneLine == null || rightEdgeLine == null)
                {
                    editor.WriteMessage("\n警告：部分边线为空，无法创建完整封口");
                    return;
                }

                // 创建起始端封口线
                CreateClosureAtEnd(leftLaneLine, leftEdgeLine, rightLaneLine, rightEdgeLine,
                                 true, currentColor, entitiesToAdd, "起始端");

                // 创建结束端封口线
                CreateClosureAtEnd(leftLaneLine, leftEdgeLine, rightLaneLine, rightEdgeLine,
                                 false, currentColor, entitiesToAdd, "结束端");

                editor.WriteMessage("\n边线封口连接线创建完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建边线封口失败: {ex.Message}");
            }
        }

        // 在指定端点创建封口线 - 删除横向封口线
        private void CreateClosureAtEnd(Curve leftLaneLine, Curve leftEdgeLine,
                                      Curve rightLaneLine, Curve rightEdgeLine,
                                      bool isStartEnd, short currentColor,
                                      List<Entity> entitiesToAdd, string description)
        {
            try
            {
                Point3d leftLanePoint, leftEdgePoint, rightLanePoint, rightEdgePoint;

                if (isStartEnd)
                {
                    // 起始端点
                    leftLanePoint = leftLaneLine.StartPoint;
                    leftEdgePoint = leftEdgeLine.StartPoint;
                    rightLanePoint = rightLaneLine.StartPoint;
                    rightEdgePoint = rightEdgeLine.StartPoint;
                }
                else
                {
                    // 结束端点
                    leftLanePoint = leftLaneLine.EndPoint;
                    leftEdgePoint = leftEdgeLine.EndPoint;
                    rightLanePoint = rightLaneLine.EndPoint;
                    rightEdgePoint = rightEdgeLine.EndPoint;
                }

                // 创建左侧封口线（连接左侧车道边界线和左侧道路边线）
                Line leftClosureLine = new Line(leftLanePoint, leftEdgePoint);
                leftClosureLine.ColorIndex = currentColor;
                entitiesToAdd.Add(leftClosureLine);

                // 创建右侧封口线（连接右侧车道边界线和右侧道路边线）
                Line rightClosureLine = new Line(rightLanePoint, rightEdgePoint);
                rightClosureLine.ColorIndex = currentColor;
                entitiesToAdd.Add(rightClosureLine);

                // 删除横向封口线 - 不再创建连接左右两侧道路边线的横向线
                // Line crossClosureLine = new Line(leftEdgePoint, rightEdgePoint);
                // crossClosureLine.ColorIndex = currentColor;
                // entitiesToAdd.Add(crossClosureLine);
            }
            catch (System.Exception ex)
            {
                // 静默处理错误，避免影响整体生成
            }
        }

        // 创建虚线起始线：分割、偏移并创建闭合矩形
        private List<Curve> CreateDashedStartLine(Curve startLine, double dashLength, double dashGap, double lineWidth,
                                                BlockTableRecord btr, Transaction trans, Editor editor)
        {
            List<Curve> dashedLines = new List<Curve>();

            try
            {
                double totalLength = GetCurveLength(startLine);

                // 计算分割点
                List<double> splitPoints = CalculateSplitPoints(totalLength, dashLength, dashGap, editor);

                // 预计算偏移距离和曲线类型检查
                double offsetDistance = lineWidth * 0.5;
                bool isStraightLine = startLine is Line;
                short currentColor = GetCurrentColor(editor);

                // 批量处理虚线段
                for (int i = 0; i < splitPoints.Count - 1; i += 2)
                {
                    double startDist = splitPoints[i];
                    double endDist = splitPoints[i + 1];

                    // 创建中心虚线段
                    Curve centerSegment = CreateLineSegment(startLine, startDist, endDist, editor);
                    if (centerSegment != null)
                    {
                        // 创建虚线矩形
                        CreateDashedRectangle(centerSegment, offsetDistance, isStraightLine, currentColor, btr, trans, editor);
                        dashedLines.Add(centerSegment);
                    }
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建虚线起始线失败: {ex.Message}");
            }

            return dashedLines;
        }

        // 计算分割点
        private List<double> CalculateSplitPoints(double totalLength, double dashLength, double dashGap, Editor editor)
        {
            List<double> splitPoints = new List<double>();

            try
            {
                double currentDistance = 0;

                while (currentDistance < totalLength)
                {
                    // 添加虚线段起始点
                    splitPoints.Add(currentDistance);

                    // 添加虚线段结束点
                    double endDistance = Math.Min(currentDistance + dashLength, totalLength);
                    splitPoints.Add(endDistance);

                    // 移动到下一个虚线段
                    currentDistance = endDistance + dashGap;
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n计算分割点失败: {ex.Message}");
            }

            return splitPoints;
        }

        // 优化的左右方向判断方法 - 缓存计算结果，减少调试输出
        private static Dictionary<string, (Vector3d rightDir, Vector3d leftDir)> _directionCache = new Dictionary<string, (Vector3d, Vector3d)>();

        private (Curve leftCurve, Curve rightCurve) GetCorrectLeftRightOffsets(Curve baseCurve, double offsetDistance, Editor editor, bool enableDebug = false)
        {
            try
            {
                // 获取基准线的方向向量（从起点到终点）
                Vector3d baseDirection = GetCurveDirection(baseCurve);

                // 使用缓存的方向计算
                string directionKey = $"{baseDirection.X:F6}_{baseDirection.Y:F6}";
                Vector3d rightDirection, leftDirection;

                if (_directionCache.ContainsKey(directionKey))
                {
                    var cached = _directionCache[directionKey];
                    rightDirection = cached.rightDir;
                    leftDirection = cached.leftDir;
                }
                else
                {
                    // 参考停止线中正确的左右方向计算方式：
                    // 右方向：new Vector3d(-roadDirection.Y, roadDirection.X, 0)
                    // 左方向：new Vector3d(roadDirection.Y, -roadDirection.X, 0)
                    rightDirection = new Vector3d(-baseDirection.Y, baseDirection.X, 0).GetNormal();
                    leftDirection = new Vector3d(baseDirection.Y, -baseDirection.X, 0).GetNormal();
                    _directionCache[directionKey] = (rightDirection, leftDirection);
                }

                if (enableDebug)
                {
                    editor.WriteMessage($"\n基准线方向: ({baseDirection.X:F3}, {baseDirection.Y:F3})");
                    editor.WriteMessage($"\n计算的右侧方向: ({rightDirection.X:F3}, {rightDirection.Y:F3})");
                    editor.WriteMessage($"\n计算的左侧方向: ({leftDirection.X:F3}, {leftDirection.Y:F3})");
                }

                // 批量获取偏移曲线
                DBObjectCollection positiveOffsets = null;
                DBObjectCollection negativeOffsets = null;

                try
                {
                    positiveOffsets = baseCurve.GetOffsetCurves(offsetDistance);
                    negativeOffsets = baseCurve.GetOffsetCurves(-offsetDistance);
                }
                catch (Exception ex)
                {
                    if (enableDebug) editor.WriteMessage($"\n偏移失败: {ex.Message}");
                    return (null, null);
                }

                if (positiveOffsets?.Count == 0 || negativeOffsets?.Count == 0)
                {
                    if (enableDebug) editor.WriteMessage("\n偏移失败，返回空结果");
                    return (null, null);
                }

                Curve positiveCurve = (Curve)positiveOffsets[0];
                Curve negativeCurve = (Curve)negativeOffsets[0];

                // 快速判断左右方向 - 使用起点而不是中点以提高性能
                Point3d basePoint = baseCurve.StartPoint;
                Point3d positivePoint = positiveCurve.StartPoint;
                Point3d negativePoint = negativeCurve.StartPoint;

                // 计算从基准点到偏移点的向量
                Vector3d toPositive = (positivePoint - basePoint);
                Vector3d toNegative = (negativePoint - basePoint);

                // 使用点积判断方向（参考停止线的正确实现）
                double positiveRightDot = toPositive.DotProduct(rightDirection);
                double negativeRightDot = toNegative.DotProduct(rightDirection);

                Curve leftCurve, rightCurve;

                if (positiveRightDot > negativeRightDot)
                {
                    rightCurve = positiveCurve;
                    leftCurve = negativeCurve;
                }
                else
                {
                    rightCurve = negativeCurve;
                    leftCurve = positiveCurve;
                }

                if (enableDebug)
                {
                    editor.WriteMessage($"\n偏移结果分析:");
                    editor.WriteMessage($"\n  基准点: ({basePoint.X:F3}, {basePoint.Y:F3})");
                    editor.WriteMessage($"\n  基准方向: ({baseDirection.X:F3}, {baseDirection.Y:F3})");
                    editor.WriteMessage($"\n  计算的右方向: ({rightDirection.X:F3}, {rightDirection.Y:F3})");
                    editor.WriteMessage($"\n  正偏移点: ({positivePoint.X:F3}, {positivePoint.Y:F3})");
                    editor.WriteMessage($"\n  负偏移点: ({negativePoint.X:F3}, {negativePoint.Y:F3})");
                    editor.WriteMessage($"\n  正偏移向量: ({toPositive.X:F3}, {toPositive.Y:F3})");
                    editor.WriteMessage($"\n  负偏移向量: ({toNegative.X:F3}, {toNegative.Y:F3})");
                    editor.WriteMessage($"\n  正偏移右侧点积: {positiveRightDot:F3}");
                    editor.WriteMessage($"\n  负偏移右侧点积: {negativeRightDot:F3}");
                    editor.WriteMessage($"\n  判断结果: positive={positiveRightDot > negativeRightDot}");
                    editor.WriteMessage($"\n左右方向判断完成");
                }

                return (leftCurve, rightCurve);
            }
            catch (System.Exception ex)
            {
                if (enableDebug) editor.WriteMessage($"\n左右方向判断失败: {ex.Message}");
                return (null, null);
            }
        }

        // 获取曲线的方向向量
        private Vector3d GetCurveDirection(Curve curve)
        {
            if (curve is Line)
            {
                Line line = (Line)curve;
                return (line.EndPoint - line.StartPoint).GetNormal();
            }
            else
            {
                // 对于非直线，使用起点和终点计算大致方向
                return (curve.EndPoint - curve.StartPoint).GetNormal();
            }
        }

        // 获取曲线的中点
        private Point3d GetCurveMidPoint(Curve curve)
        {
            try
            {
                double totalLength = GetCurveLength(curve);
                return GetPointAtDistance(curve, totalLength * 0.5);
            }
            catch
            {
                // 备用方法：使用起点和终点的中点
                return new Point3d(
                    (curve.StartPoint.X + curve.EndPoint.X) * 0.5,
                    (curve.StartPoint.Y + curve.EndPoint.Y) * 0.5,
                    (curve.StartPoint.Z + curve.EndPoint.Z) * 0.5
                );
            }
        }

        // 创建线段
        private Curve CreateLineSegment(Curve baseCurve, double startDist, double endDist, Editor editor)
        {
            try
            {
                Point3d startPoint = GetPointAtDistance(baseCurve, startDist);
                Point3d endPoint = GetPointAtDistance(baseCurve, endDist);

                return new Line(startPoint, endPoint);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建线段失败: {ex.Message}");
                return null;
            }
        }

        // 创建虚线矩形 - 使用统一的左右方向判断
        private void CreateDashedRectangle(Curve centerSegment, double offsetDistance, bool isStraightLine,
                                         short currentColor, BlockTableRecord btr, Transaction trans, Editor editor)
        {
            try
            {
                // 使用统一的左右方向判断
                var offsets = GetCorrectLeftRightOffsets(centerSegment, offsetDistance, editor);

                if (offsets.leftCurve != null && offsets.rightCurve != null)
                {
                    Curve leftLine = offsets.leftCurve;
                    Curve rightLine = offsets.rightCurve;

                    leftLine.ColorIndex = currentColor;
                    rightLine.ColorIndex = currentColor;

                    btr.AppendEntity(leftLine);
                    btr.AppendEntity(rightLine);
                    trans.AddNewlyCreatedDBObject(leftLine, true);
                    trans.AddNewlyCreatedDBObject(rightLine, true);

                    // 创建连接线
                    Line startConnection = new Line(leftLine.StartPoint, rightLine.StartPoint);
                    Line endConnection = new Line(leftLine.EndPoint, rightLine.EndPoint);

                    startConnection.ColorIndex = currentColor;
                    endConnection.ColorIndex = currentColor;

                    btr.AppendEntity(startConnection);
                    btr.AppendEntity(endConnection);
                    trans.AddNewlyCreatedDBObject(startConnection, true);
                    trans.AddNewlyCreatedDBObject(endConnection, true);

                    editor.WriteMessage("\n虚线矩形创建成功（使用统一左右判断）");
                }
                else
                {
                    editor.WriteMessage("\n虚线矩形创建失败：无法获取有效的偏移曲线");
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建虚线矩形失败: {ex.Message}");
            }
        }

        // 重写的停止线生成算法 - 基于长虚线段端点的精确停止线创建
        private void CreateStopLinesFixed(Curve startLine, Curve leftLaneLine, Curve rightLaneLine,
                                        double lineWidth, double laneWidth, double edgeLineWidth,
                                        double bikeLineWidth, double stopLineWidth, short currentColor,
                                        List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 重写的停止线生成算法 ===");

                // 从textBox控件读取所有参数 - 修复：正确对照窗体数据索引
                double dashLineWidth = GetDoubleValue(this.textBox8, 0.15);    // 车道虚线宽度 (textBox8)
                double singleLaneWidth = GetDoubleValue(this.textBox2, 3.75);  // 单车道宽度 (textBox2) - 修复错误
                double edgeLineWidthParam = GetDoubleValue(this.textBox7, 0.15); // 车道边线宽度 (textBox7)
                double bikeLineWidthParam = GetDoubleValue(this.textBox6, 2.5);  // 非机动车道宽度 (textBox6)
                double stopLineWidthParam = GetDoubleValue(this.textBox13, 0.2); // 停止线宽度 (textBox13)

                // 修复：正确的停止线总长度计算公式
                // 根据窗体标签：车道虚线宽度 + 单车道宽度 + 车道边线宽度 + 非机动车道宽度
                double stopLineLength = dashLineWidth + singleLaneWidth + edgeLineWidthParam + bikeLineWidthParam;

                editor.WriteMessage($"\n停止线参数（修复后）:");
                editor.WriteMessage($"\n  车道虚线宽度 (textBox8): {dashLineWidth:F3}");
                editor.WriteMessage($"\n  单车道宽度 (textBox2): {singleLaneWidth:F3}");
                editor.WriteMessage($"\n  车道边线宽度 (textBox7): {edgeLineWidthParam:F3}");
                editor.WriteMessage($"\n  非机动车道宽度 (textBox6): {bikeLineWidthParam:F3}");
                editor.WriteMessage($"\n  停止线宽度 (textBox13): {stopLineWidthParam:F3}");
                editor.WriteMessage($"\n  停止线总长度: {stopLineLength:F3}");

                // 获取虚线布局信息以确定长虚线段位置
                var dashLayout = GetDashLayoutInfo(startLine, editor);
                if (dashLayout == null)
                {
                    editor.WriteMessage("\n无法获取虚线布局信息，停止线创建失败");
                    return;
                }

                // 第一步：创建起始端停止线
                CreateStartStopLine(startLine, dashLayout, stopLineLength, stopLineWidthParam, currentColor, entitiesToAdd, editor);

                // 第二步：创建结束端停止线
                CreateEndStopLine(startLine, dashLayout, stopLineLength, stopLineWidthParam, currentColor, entitiesToAdd, editor);

                editor.WriteMessage($"\n停止线生成算法完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n停止线生成失败: {ex.Message}");
            }
        }

        // 获取虚线布局信息
        private DashLayout GetDashLayoutInfo(Curve startLine, Editor editor)
        {
            try
            {
                double totalLength = GetCurveLength(startLine);
                double dashLength = GetDoubleValue(this.textBox9, 6.0);
                double dashGap = GetDoubleValue(this.textBox15, 9.0);

                // 长虚线长度范围 - 修复：最大长度应包含2个间隔
                double minLongDash = dashLength * 2 + dashGap * 1;      // 最小：2段虚线 + 1个间隔
                double maxLongDash = dashLength * 3 + dashGap * 2;      // 最大：3段虚线 + 2个间隔

                // 使用现有的严格优先级布局算法
                return CalculateStrictPriorityLayout(totalLength, dashLength, dashGap, minLongDash, maxLongDash, editor);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n获取虚线布局信息失败: {ex.Message}");
                return null;
            }
        }

        // 第二步：起始端停止线生成 - 按照用户精确要求
        private void CreateStartStopLine(Curve startLine, DashLayout dashLayout, double stopLineLength,
                                       double stopLineWidth, short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 第二步：起始端停止线生成 ===");

                // 1. 以起始线起始点左侧的长虚线的起始点为端点
                double leftLongDashStartDistance = 0; // 左侧长虚线从基准线起始点开始
                Point3d leftLongDashStartPoint = GetPointAtDistance(startLine, leftLongDashStartDistance);

                editor.WriteMessage($"\n虚线布局信息:");
                editor.WriteMessage($"\n  左侧长虚线起始点: ({leftLongDashStartPoint.X:F3}, {leftLongDashStartPoint.Y:F3})");

                // 2. 获取道路方向
                Vector3d roadDirection = GetTangentAtPoint(startLine, leftLongDashStartPoint, true);
                editor.WriteMessage($"\n道路方向: ({roadDirection.X:F3}, {roadDirection.Y:F3})");

                // 3. 计算垂直于道路的方向（向右）
                Vector3d rightDirection = new Vector3d(-roadDirection.Y, roadDirection.X, 0).GetNormal();
                editor.WriteMessage($"\n垂直于道路方向（向右）: ({rightDirection.X:F3}, {rightDirection.Y:F3})");

                // 4. 向右生成一条垂直于道路的直线
                // 直线长度=车道虚线宽度+单车道宽度+车道边线宽度+非机动车道宽度
                Point3d stopLineStart = leftLongDashStartPoint;
                Point3d stopLineEnd = leftLongDashStartPoint + rightDirection * stopLineLength;

                editor.WriteMessage($"\n起始端停止线:");
                editor.WriteMessage($"\n  起点（左侧长虚线起始点）: ({stopLineStart.X:F3}, {stopLineStart.Y:F3})");
                editor.WriteMessage($"\n  终点: ({stopLineEnd.X:F3}, {stopLineEnd.Y:F3})");
                editor.WriteMessage($"\n  长度: {stopLineLength:F3}");

                // 5. 使用offset偏移命令创建矩形停止线
                CreateStopLineWithOffset(stopLineStart, stopLineEnd, stopLineWidth, roadDirection,
                                       true, currentColor, entitiesToAdd, editor);

                // 6. 对生成的停止线进行移动 - 起始点附近的停止线向左移动
                MoveStopLine(entitiesToAdd, stopLineStart, stopLineEnd, true, editor);

                editor.WriteMessage($"\n起始端停止线创建完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建起始端停止线失败: {ex.Message}");
            }
        }

        // 第三步：结束端停止线生成 - 按照用户精确要求
        private void CreateEndStopLine(Curve startLine, DashLayout dashLayout, double stopLineLength,
                                     double stopLineWidth, short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 第三步：结束端停止线生成 ===");

                // 1. 以起始线结束点右侧的长虚线的结束点为端点
                double totalLength = GetCurveLength(startLine);
                double rightLongDashEndDistance = totalLength; // 右侧长虚线结束点就是基准线结束点
                Point3d rightLongDashEndPoint = GetPointAtDistance(startLine, rightLongDashEndDistance);

                editor.WriteMessage($"\n虚线布局信息:");
                editor.WriteMessage($"\n  右侧长虚线结束点: ({rightLongDashEndPoint.X:F3}, {rightLongDashEndPoint.Y:F3})");

                // 2. 获取道路方向
                Vector3d roadDirection = GetTangentAtPoint(startLine, rightLongDashEndPoint, false);
                editor.WriteMessage($"\n道路方向: ({roadDirection.X:F3}, {roadDirection.Y:F3})");

                // 3. 计算垂直于道路的方向（向左）
                Vector3d leftDirection = new Vector3d(roadDirection.Y, -roadDirection.X, 0).GetNormal();
                editor.WriteMessage($"\n垂直于道路方向（向左）: ({leftDirection.X:F3}, {leftDirection.Y:F3})");

                // 4. 向左生成一条垂直于道路的直线
                // 直线长度=车道虚线宽度+单车道宽度+车道边线宽度+非机动车道宽度
                Point3d stopLineStart = rightLongDashEndPoint;
                Point3d stopLineEnd = rightLongDashEndPoint + leftDirection * stopLineLength;

                editor.WriteMessage($"\n结束端停止线:");
                editor.WriteMessage($"\n  起点（右侧长虚线结束点）: ({stopLineStart.X:F3}, {stopLineStart.Y:F3})");
                editor.WriteMessage($"\n  终点: ({stopLineEnd.X:F3}, {stopLineEnd.Y:F3})");
                editor.WriteMessage($"\n  长度: {stopLineLength:F3}");

                // 5. 使用offset偏移命令创建矩形停止线
                CreateStopLineWithOffset(stopLineStart, stopLineEnd, stopLineWidth, roadDirection,
                                       false, currentColor, entitiesToAdd, editor);

                // 6. 对生成的停止线进行移动 - 结束点附近的停止线向右移动
                MoveStopLine(entitiesToAdd, stopLineStart, stopLineEnd, false, editor);

                editor.WriteMessage($"\n结束端停止线创建完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建结束端停止线失败: {ex.Message}");
            }
        }

        // 对生成的停止线进行移动
        private void MoveStopLine(List<Entity> entitiesToAdd, Point3d stopLineStart, Point3d stopLineEnd,
                                bool isStartEnd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 停止线移动操作 ===");
                editor.WriteMessage($"\n位置: {(isStartEnd ? "起始端" : "结束端")}");

                // 1. 获取车道虚线宽度参数
                double dashLineWidth = GetDoubleValue(this.textBox8, 0.15); // 车道虚线宽度
                double moveDistance = dashLineWidth * 0.5; // 移动距离=车道虚线宽度*0.5

                editor.WriteMessage($"\n车道虚线宽度: {dashLineWidth:F3}");
                editor.WriteMessage($"\n移动距离: {moveDistance:F3}");

                // 2. 计算停止线长边方向
                Vector3d stopLineDirection = (stopLineEnd - stopLineStart).GetNormal();
                editor.WriteMessage($"\n停止线长边方向: ({stopLineDirection.X:F3}, {stopLineDirection.Y:F3})");

                // 3. 确定移动方向
                Vector3d moveDirection;
                if (isStartEnd)
                {
                    // 起始端停止线向左移动
                    // 起始端：stopLineDirection = rightDirection，向左移动 = -rightDirection
                    moveDirection = -stopLineDirection;
                    editor.WriteMessage($"\n起始端停止线向左移动");
                }
                else
                {
                    // 结束端停止线向右移动 - 修正方向
                    // 结束端：stopLineDirection = leftDirection，向右移动 = -leftDirection
                    moveDirection = -stopLineDirection;
                    editor.WriteMessage($"\n结束端停止线向右移动（修正方向）");
                }

                editor.WriteMessage($"\n移动方向: ({moveDirection.X:F3}, {moveDirection.Y:F3})");

                // 4. 计算移动向量
                Vector3d moveVector = moveDirection * moveDistance;
                editor.WriteMessage($"\n移动向量: ({moveVector.X:F3}, {moveVector.Y:F3})");

                // 5. 获取最近添加的停止线实体（最后4个实体是刚创建的停止线矩形）
                int stopLineEntityCount = 4; // 停止线矩形包含4条边
                int startIndex = Math.Max(0, entitiesToAdd.Count - stopLineEntityCount);

                editor.WriteMessage($"\n移动停止线实体，从索引 {startIndex} 开始，共 {stopLineEntityCount} 个实体");

                // 6. 对停止线的所有实体进行移动
                for (int i = startIndex; i < entitiesToAdd.Count; i++)
                {
                    if (entitiesToAdd[i] is Line line)
                    {
                        // 移动线段的起点和终点
                        Point3d newStartPoint = line.StartPoint + moveVector;
                        Point3d newEndPoint = line.EndPoint + moveVector;

                        // 创建新的移动后的线段
                        Line movedLine = new Line(newStartPoint, newEndPoint);
                        movedLine.ColorIndex = line.ColorIndex;

                        // 替换原来的线段
                        entitiesToAdd[i] = movedLine;

                        editor.WriteMessage($"\n  线段 {i}: ({line.StartPoint.X:F3}, {line.StartPoint.Y:F3}) -> ({line.EndPoint.X:F3}, {line.EndPoint.Y:F3})");
                        editor.WriteMessage($"\n    移动后: ({newStartPoint.X:F3}, {newStartPoint.Y:F3}) -> ({newEndPoint.X:F3}, {newEndPoint.Y:F3})");
                    }
                }

                editor.WriteMessage($"\n停止线移动完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n停止线移动失败: {ex.Message}");
            }
        }

        // 使用offset偏移命令创建停止线矩形 - 严格按照用户要求
        private void CreateStopLineWithOffset(Point3d stopLineStart, Point3d stopLineEnd, double stopLineWidth,
                                            Vector3d roadDirection, bool isStartEnd, short currentColor,
                                            List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 使用offset偏移命令创建停止线矩形 ===");
                editor.WriteMessage($"\n停止线起点: ({stopLineStart.X:F3}, {stopLineStart.Y:F3})");
                editor.WriteMessage($"\n停止线终点: ({stopLineEnd.X:F3}, {stopLineEnd.Y:F3})");
                editor.WriteMessage($"\n停止线宽度: {stopLineWidth:F3}");
                editor.WriteMessage($"\n位置: {(isStartEnd ? "起始端" : "结束端")}");

                // 1. 创建原始停止线
                Line originalStopLine = new Line(stopLineStart, stopLineEnd);
                editor.WriteMessage($"\n原始停止线创建完成");

                // 2. 使用AutoCAD的offset命令向车道外侧偏移
                // 确定偏移方向：向车道外侧
                Vector3d offsetDirection;
                if (isStartEnd)
                {
                    // 起始端：向道路起始方向偏移（车道外侧）
                    offsetDirection = -roadDirection;
                }
                else
                {
                    // 结束端：向道路结束方向偏移（车道外侧）
                    offsetDirection = roadDirection;
                }

                editor.WriteMessage($"\n偏移方向（向车道外侧）: ({offsetDirection.X:F3}, {offsetDirection.Y:F3})");

                // 3. 使用GetOffsetCurves方法进行偏移
                DBObjectCollection offsetCurves = null;
                Line offsetStopLine = null;

                try
                {
                    // 计算偏移距离（向车道外侧）
                    double offsetDistance = stopLineWidth;
                    offsetCurves = originalStopLine.GetOffsetCurves(offsetDistance);

                    if (offsetCurves != null && offsetCurves.Count > 0)
                    {
                        offsetStopLine = (Line)offsetCurves[0];
                        editor.WriteMessage($"\nAutoCAD offset成功");
                        editor.WriteMessage($"\n偏移线: ({offsetStopLine.StartPoint.X:F3}, {offsetStopLine.StartPoint.Y:F3}) -> ({offsetStopLine.EndPoint.X:F3}, {offsetStopLine.EndPoint.Y:F3})");
                    }
                    else
                    {
                        throw new System.Exception("GetOffsetCurves返回空结果");
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\nAutoCAD offset失败: {ex.Message}，使用手动偏移");

                    // 手动偏移作为备用方案
                    Point3d offsetStart = stopLineStart + offsetDirection * stopLineWidth;
                    Point3d offsetEnd = stopLineEnd + offsetDirection * stopLineWidth;
                    offsetStopLine = new Line(offsetStart, offsetEnd);

                    editor.WriteMessage($"\n手动偏移线: ({offsetStart.X:F3}, {offsetStart.Y:F3}) -> ({offsetEnd.X:F3}, {offsetEnd.Y:F3})");
                }

                // 4. 连接偏移前和偏移后的两条直线的端点进行封口，获得一个矩形
                Line[] rectangleLines = new Line[]
                {
                    originalStopLine,                                                    // 原始停止线
                    new Line(originalStopLine.EndPoint, offsetStopLine.EndPoint),      // 右侧连接线
                    offsetStopLine,                                                     // 偏移后的停止线
                    new Line(offsetStopLine.StartPoint, originalStopLine.StartPoint)   // 左侧连接线
                };

                // 5. 添加所有线段到实体列表
                foreach (Line line in rectangleLines)
                {
                    line.ColorIndex = currentColor;
                    entitiesToAdd.Add(line);
                }

                editor.WriteMessage($"\n停止线矩形创建成功，包含4条边");
                editor.WriteMessage($"\n  原始停止线: ({originalStopLine.StartPoint.X:F3}, {originalStopLine.StartPoint.Y:F3}) -> ({originalStopLine.EndPoint.X:F3}, {originalStopLine.EndPoint.Y:F3})");
                editor.WriteMessage($"\n  偏移停止线: ({offsetStopLine.StartPoint.X:F3}, {offsetStopLine.StartPoint.Y:F3}) -> ({offsetStopLine.EndPoint.X:F3}, {offsetStopLine.EndPoint.Y:F3})");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n使用offset创建停止线矩形失败: {ex.Message}");
            }
        }

        // 创建正确的停止线矩形 - 垂直于基准线的短线
        private void CreateCorrectStopLineRectangle(Point3d stopLineStart, Point3d stopLineEnd, double stopLineWidth,
                                                  Vector3d roadDirection, bool isStartEnd, short currentColor,
                                                  List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 创建正确的停止线矩形 ===");
                editor.WriteMessage($"\n停止线起点: ({stopLineStart.X:F3}, {stopLineStart.Y:F3})");
                editor.WriteMessage($"\n停止线终点: ({stopLineEnd.X:F3}, {stopLineEnd.Y:F3})");
                editor.WriteMessage($"\n停止线宽度: {stopLineWidth:F3}");
                editor.WriteMessage($"\n位置: {(isStartEnd ? "起始端" : "结束端")}");

                // 1. 停止线方向（垂直于基准线）
                Vector3d stopLineDirection = (stopLineEnd - stopLineStart).GetNormal();
                editor.WriteMessage($"\n停止线方向（垂直于基准线）: ({stopLineDirection.X:F3}, {stopLineDirection.Y:F3})");

                // 2. 停止线宽度方向（沿基准线方向，即道路方向）
                Vector3d widthDirection = roadDirection.GetNormal();
                editor.WriteMessage($"\n宽度方向（沿基准线/道路方向）: ({widthDirection.X:F3}, {widthDirection.Y:F3})");

                // 3. 计算停止线矩形的四个顶点
                // 停止线矩形沿基准线方向（道路方向）扩展宽度
                Point3d p1 = stopLineStart; // 停止线起点
                Point3d p2 = stopLineEnd;   // 停止线终点
                Point3d p3 = stopLineEnd + widthDirection * stopLineWidth;   // 终点沿道路方向偏移
                Point3d p4 = stopLineStart + widthDirection * stopLineWidth; // 起点沿道路方向偏移

                editor.WriteMessage($"\n停止线矩形顶点:");
                editor.WriteMessage($"\n  P1 (停止线起点): ({p1.X:F3}, {p1.Y:F3})");
                editor.WriteMessage($"\n  P2 (停止线终点): ({p2.X:F3}, {p2.Y:F3})");
                editor.WriteMessage($"\n  P3 (终点偏移): ({p3.X:F3}, {p3.Y:F3})");
                editor.WriteMessage($"\n  P4 (起点偏移): ({p4.X:F3}, {p4.Y:F3})");

                // 4. 创建封闭的矩形停止线
                Line[] rectangleLines = new Line[]
                {
                    new Line(p1, p2),  // 停止线主线（垂直于基准线）
                    new Line(p2, p3),  // 右侧边（沿道路方向）
                    new Line(p3, p4),  // 偏移线（平行于停止线主线）
                    new Line(p4, p1)   // 左侧边（沿道路方向）
                };

                // 5. 添加所有线段到实体列表
                foreach (Line line in rectangleLines)
                {
                    line.ColorIndex = currentColor;
                    entitiesToAdd.Add(line);
                }

                editor.WriteMessage($"\n正确停止线矩形创建成功，包含4条边");
                editor.WriteMessage($"\n  停止线主线（垂直于基准线）: ({p1.X:F3}, {p1.Y:F3}) -> ({p2.X:F3}, {p2.Y:F3})");
                editor.WriteMessage($"\n  偏移线（平行于主线）: ({p4.X:F3}, {p4.Y:F3}) -> ({p3.X:F3}, {p3.Y:F3})");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建正确停止线矩形失败: {ex.Message}");
            }
        }

        // 完全重写的停止线矩形创建方法 - 修复偏移方向错误
        private void CreateStopLineRectangleFixed(Line baseLine, double stopLineWidth, bool isStartEnd,
                                                short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 创建停止线矩形（完全重写） ===");
                editor.WriteMessage($"\n基准线: ({baseLine.StartPoint.X:F3}, {baseLine.StartPoint.Y:F3}) -> ({baseLine.EndPoint.X:F3}, {baseLine.EndPoint.Y:F3})");
                editor.WriteMessage($"\n停止线宽度: {stopLineWidth:F3}");
                editor.WriteMessage($"\n位置: {(isStartEnd ? "起始端" : "结束端")}");

                // 关键修复：停止线应该垂直于基准线方向，而不是沿着基准线方向
                // 基准线是垂直于道路的，所以停止线的宽度方向应该沿着道路方向

                // 1. 获取基准线的方向（垂直于道路）
                Vector3d baseLineDirection = (baseLine.EndPoint - baseLine.StartPoint).GetNormal();
                editor.WriteMessage($"\n基准线方向（垂直于道路）: ({baseLineDirection.X:F3}, {baseLineDirection.Y:F3})");

                // 2. 计算道路方向（垂直于基准线）
                Vector3d roadDirection = new Vector3d(-baseLineDirection.Y, baseLineDirection.X, 0).GetNormal();
                editor.WriteMessage($"\n道路方向: ({roadDirection.X:F3}, {roadDirection.Y:F3})");

                // 3. 停止线的宽度方向就是道路方向 - 修复起始端偏移方向
                Vector3d stopLineWidthDirection;
                if (isStartEnd)
                {
                    // 修复：起始端偏移方向反过来 - 向道路结束方向扩展宽度
                    stopLineWidthDirection = roadDirection;
                    editor.WriteMessage($"\n起始端停止线宽度方向（修复后）: ({stopLineWidthDirection.X:F3}, {stopLineWidthDirection.Y:F3})");
                }
                else
                {
                    // 结束端：向道路起始方向扩展宽度（保持原来的逻辑）
                    stopLineWidthDirection = -roadDirection;
                    editor.WriteMessage($"\n结束端停止线宽度方向: ({stopLineWidthDirection.X:F3}, {stopLineWidthDirection.Y:F3})");
                }

                // 4. 计算停止线矩形的四个顶点
                Point3d p1 = baseLine.StartPoint; // 基准线起点
                Point3d p2 = baseLine.EndPoint;   // 基准线终点
                Point3d p3 = baseLine.EndPoint + stopLineWidthDirection * stopLineWidth;   // 偏移后的终点
                Point3d p4 = baseLine.StartPoint + stopLineWidthDirection * stopLineWidth; // 偏移后的起点

                editor.WriteMessage($"\n停止线矩形顶点（偏移前）:");
                editor.WriteMessage($"\n  P1 (基准线起点): ({p1.X:F3}, {p1.Y:F3})");
                editor.WriteMessage($"\n  P2 (基准线终点): ({p2.X:F3}, {p2.Y:F3})");
                editor.WriteMessage($"\n  P3 (偏移后终点): ({p3.X:F3}, {p3.Y:F3})");
                editor.WriteMessage($"\n  P4 (偏移后起点): ({p4.X:F3}, {p4.Y:F3})");

                // 5. 创建封闭的矩形停止线（移除移动操作）
                Line[] rectangleLines = new Line[]
                {
                    new Line(p1, p2),  // 基准线（道路内侧边）
                    new Line(p2, p3),  // 右侧边
                    new Line(p3, p4),  // 偏移线（道路外侧边）
                    new Line(p4, p1)   // 左侧边
                };

                // 6. 添加所有线段到实体列表
                foreach (Line line in rectangleLines)
                {
                    line.ColorIndex = currentColor;
                    entitiesToAdd.Add(line);
                }

                editor.WriteMessage($"\n停止线矩形创建成功，包含4条边");
                editor.WriteMessage($"\n  基准线（道路内侧）: ({p1.X:F3}, {p1.Y:F3}) -> ({p2.X:F3}, {p2.Y:F3})");
                editor.WriteMessage($"\n  偏移线（道路外侧）: ({p4.X:F3}, {p4.Y:F3}) -> ({p3.X:F3}, {p3.Y:F3})");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建停止线矩形失败: {ex.Message}");
            }
        }

        // 创建停止线矩形（使用AutoCAD的offset命令）
        private void CreateStopLineRectangle(Line baseLine, double stopLineWidth, bool isStartEnd,
                                           short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 创建停止线矩形 ===");
                editor.WriteMessage($"\n基准线: ({baseLine.StartPoint.X:F3}, {baseLine.StartPoint.Y:F3}) -> ({baseLine.EndPoint.X:F3}, {baseLine.EndPoint.Y:F3})");
                editor.WriteMessage($"\n停止线宽度: {stopLineWidth:F3}");
                editor.WriteMessage($"\n位置: {(isStartEnd ? "起始端" : "结束端")}");

                // 使用AutoCAD的GetOffsetCurves方法进行精确偏移
                DBObjectCollection offsetCurves = null;
                try
                {
                    // 确定偏移方向：起始端向后偏移，结束端向前偏移
                    double offsetDistance = isStartEnd ? -stopLineWidth : stopLineWidth;
                    offsetCurves = baseLine.GetOffsetCurves(offsetDistance);

                    if (offsetCurves != null && offsetCurves.Count > 0)
                    {
                        Line offsetLine = (Line)offsetCurves[0];
                        editor.WriteMessage($"\n偏移线创建成功: ({offsetLine.StartPoint.X:F3}, {offsetLine.StartPoint.Y:F3}) -> ({offsetLine.EndPoint.X:F3}, {offsetLine.EndPoint.Y:F3})");

                        // 创建封闭的矩形停止线
                        Line[] rectangleLines = new Line[]
                        {
                            baseLine,                                                    // 基准线
                            new Line(baseLine.EndPoint, offsetLine.EndPoint),          // 右侧连接线
                            offsetLine,                                                 // 偏移线
                            new Line(offsetLine.StartPoint, baseLine.StartPoint)       // 左侧连接线
                        };

                        // 添加所有线段到实体列表
                        foreach (Line line in rectangleLines)
                        {
                            line.ColorIndex = currentColor;
                            entitiesToAdd.Add(line);
                        }

                        editor.WriteMessage($"\n停止线矩形创建成功，包含4条边");
                    }
                    else
                    {
                        editor.WriteMessage($"\n偏移失败，使用备用方法");
                        CreateStopLineRectangleManual(baseLine, stopLineWidth, isStartEnd, currentColor, entitiesToAdd, editor);
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n AutoCAD偏移失败: {ex.Message}，使用备用方法");
                    CreateStopLineRectangleManual(baseLine, stopLineWidth, isStartEnd, currentColor, entitiesToAdd, editor);
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建停止线矩形失败: {ex.Message}");
            }
        }

        // 手动创建停止线矩形（备用方法）
        private void CreateStopLineRectangleManual(Line baseLine, double stopLineWidth, bool isStartEnd,
                                                 short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                // 计算基准线方向
                Vector3d lineDirection = (baseLine.EndPoint - baseLine.StartPoint).GetNormal();

                // 计算偏移方向
                Vector3d offsetDirection;
                if (isStartEnd)
                {
                    // 起始端：向基准线起始方向偏移
                    offsetDirection = -lineDirection;
                }
                else
                {
                    // 结束端：向基准线结束方向偏移
                    offsetDirection = lineDirection;
                }

                // 计算偏移后的点
                Point3d offsetStart = baseLine.StartPoint + offsetDirection * stopLineWidth;
                Point3d offsetEnd = baseLine.EndPoint + offsetDirection * stopLineWidth;

                // 创建矩形的四条边
                Line[] rectangleLines = new Line[]
                {
                    baseLine,                                           // 基准线
                    new Line(baseLine.EndPoint, offsetEnd),           // 右侧连接线
                    new Line(offsetEnd, offsetStart),                 // 偏移线
                    new Line(offsetStart, baseLine.StartPoint)        // 左侧连接线
                };

                // 添加所有线段到实体列表
                foreach (Line line in rectangleLines)
                {
                    line.ColorIndex = currentColor;
                    entitiesToAdd.Add(line);
                }

                editor.WriteMessage($"\n手动停止线矩形创建成功");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n手动创建停止线矩形失败: {ex.Message}");
            }
        }

        // 获取曲线在指定点的切线方向
        private Vector3d GetTangentAtPoint(Curve curve, Point3d point, bool isStart)
        {
            try
            {
                if (curve is Line line)
                {
                    // 对于直线，切线方向就是线段方向
                    return (line.EndPoint - line.StartPoint).GetNormal();
                }
                else
                {
                    // 对于多段线、曲线、样条曲线和圆弧，需要修正方向判断
                    // 根据用户反馈，这些曲线类型的左右方向判断是反的，需要互换
                    bool correctedIsStart = !isStart; // 反转方向判断

                    double param = correctedIsStart ? curve.StartParam : curve.EndParam;
                    Vector3d derivative = curve.GetFirstDerivative(param);
                    return derivative.GetNormal();
                }
            }
            catch (System.Exception ex)
            {
                // 备用方法：使用线段方向
                return (curve.EndPoint - curve.StartPoint).GetNormal();
            }
        }





        // 获取基准线在指定点的方向
        private Vector3d GetDirectionAtPoint(Curve baseLine, Point3d point, bool isStartEnd)
        {
            try
            {
                if (baseLine is Line)
                {
                    // 对于直线，方向是固定的
                    Line line = (Line)baseLine;
                    return (line.EndPoint - line.StartPoint).GetNormal();
                }
                else
                {
                    // 对于曲线，获取在指定点的切线方向
                    if (isStartEnd)
                    {
                        // 起始端，使用起始点的切线方向
                        return GetCurveDirection(baseLine);
                    }
                    else
                    {
                        // 结束端，使用结束点的切线方向
                        // 对于复杂曲线，可以使用参数化方法获取精确方向
                        return GetCurveDirection(baseLine);
                    }
                }
            }
            catch
            {
                // 备用方法：使用整体方向
                return GetCurveDirection(baseLine);
            }
        }

        // 第一步：单车道左侧边线（修改第二次偏移方法 + 对象生命周期管理）
        private void CreateLeftSideLaneEdges(Curve startLine, double lineWidth, double laneWidth, double edgeLineWidth,
                                           short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 第一步：单车道左侧边线算法（增强对象管理） ===");
                editor.WriteMessage($"\n原始起始线类型: {startLine.GetType().Name}");

                // 1. 计算起始端停止线外侧边线位置
                Point3d stopLineOuterPosition = CalculateStopLineOuterEdgePosition(startLine, true, editor);

                // 2. 使用extend命令将起始线向起始点方向延长到停止线外侧边线处
                Curve extendedLine = ExtendStartLineToPosition(startLine, stopLineOuterPosition, true, editor);

                // 2.1 验证延长后的线条有效性
                if (extendedLine == null)
                {
                    editor.WriteMessage("\n错误：延长线条失败，extendedLine为null");
                    return;
                }

                editor.WriteMessage($"\n延长后线条类型: {extendedLine.GetType().Name}");
                editor.WriteMessage($"\n延长后线条长度: {extendedLine.GetDistanceAtParameter(extendedLine.EndParam):F3}");

                // 2.2 检查对象状态
                try
                {
                    bool isDisposed = extendedLine.IsDisposed;
                    editor.WriteMessage($"\n延长线条IsDisposed状态: {isDisposed}");
                    if (isDisposed)
                    {
                        editor.WriteMessage("\n错误：延长线条已被释放");
                        return;
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n检查延长线条状态失败: {ex.Message}");
                }

                // 3. 计算内边线偏移距离：车道虚线宽度*0.5+单车道宽度
                double innerOffset = (lineWidth * 0.5) + laneWidth;
                editor.WriteMessage($"\n内边线偏移距离: {innerOffset:F3}");

                // 4. 第一次偏移：向左偏移生成单车道左侧内边线
                editor.WriteMessage($"\n=== 开始第一次偏移（内边线） ===");
                Curve leftInnerEdge = CreateSingleOffsetCurve(extendedLine, -innerOffset, currentColor, editor, "左侧内边线");
                if (leftInnerEdge == null)
                {
                    editor.WriteMessage("\n左侧内边线生成失败");
                    return;
                }
                entitiesToAdd.Add(leftInnerEdge);
                editor.WriteMessage($"\n第一次偏移成功，内边线类型: {leftInnerEdge.GetType().Name}");

                // 4.1 第一次偏移后再次检查extendedLine状态
                editor.WriteMessage($"\n=== 第一次偏移后检查extendedLine状态 ===");
                try
                {
                    bool isDisposedAfterFirst = extendedLine.IsDisposed;
                    editor.WriteMessage($"\n第一次偏移后extendedLine.IsDisposed: {isDisposedAfterFirst}");

                    if (!isDisposedAfterFirst)
                    {
                        double lengthAfterFirst = extendedLine.GetDistanceAtParameter(extendedLine.EndParam);
                        editor.WriteMessage($"\n第一次偏移后extendedLine长度: {lengthAfterFirst:F3}");
                        editor.WriteMessage($"\n第一次偏移后extendedLine类型: {extendedLine.GetType().Name}");
                    }
                    else
                    {
                        editor.WriteMessage("\n警告：第一次偏移后extendedLine已被释放！");
                        return;
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n第一次偏移后检查extendedLine状态失败: {ex.Message}");
                    return;
                }

                // 5. 新方法：直接从延长后的起始线偏移生成外边线
                // 计算外边线偏移距离：车道虚线宽度*0.5+单车道宽度+车道边线宽度
                double outerOffset = (lineWidth * 0.5) + laneWidth + edgeLineWidth;
                editor.WriteMessage($"\n外边线偏移距离（新方法）: {outerOffset:F3}");
                editor.WriteMessage($"\n计算公式：车道虚线宽度*0.5({lineWidth * 0.5:F3}) + 单车道宽度({laneWidth:F3}) + 车道边线宽度({edgeLineWidth:F3})");

                // 6. 第二次偏移：直接从延长后的起始线向左偏移生成左侧外边线
                editor.WriteMessage($"\n=== 开始第二次偏移（外边线） ===");
                Curve leftOuterEdge = CreateSingleOffsetCurve(extendedLine, -outerOffset, currentColor, editor, "左侧外边线（新方法）");
                if (leftOuterEdge == null)
                {
                    editor.WriteMessage("\n左侧外边线生成失败");
                    return;
                }
                entitiesToAdd.Add(leftOuterEdge);
                editor.WriteMessage($"\n第二次偏移成功，外边线类型: {leftOuterEdge.GetType().Name}");

                // 7. 连接单车道左侧内边线和单车道左侧外边线的端点封口
                CreateTwoLinesClosure(leftInnerEdge, leftOuterEdge, currentColor, entitiesToAdd, editor);

                // 注意：禁止用新生成的线条代替单车道左侧内边线
                editor.WriteMessage("\n注意：新生成的边线不会替换原有的车道边界线");
                editor.WriteMessage("\n左侧边线算法完成（使用新的偏移方法 + 增强对象管理）");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建左侧边线失败: {ex.Message}");
                editor.WriteMessage($"\n错误堆栈: {ex.StackTrace}");
            }
        }

        // 边线延长和封口处理
        private void CreateEdgeLineExtensionAndClosure(Curve rightLaneLine, Curve rightEdgeLine,
                                                     Curve leftLaneLine, Curve leftEdgeLine,
                                                     Curve baseLine, BlockTableRecord btr,
                                                     Transaction trans, Editor editor)
        {
            try
            {
                // 这里可以添加边线延长和封口处理逻辑
                editor.WriteMessage("\n边线延长和封口处理功能待实现");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n边线延长和封口处理失败: {ex.Message}");
            }
        }

        // 创建组合曲线：将起始线和连接线组合
        private Curve CreateCombinedCurve(Curve startLine, Line connectionLine, Editor editor)
        {
            try
            {
                // 对于简单情况，我们可以直接使用起始线
                // 在更复杂的实现中，可能需要创建复合曲线
                editor.WriteMessage("\n使用起始线作为基准进行偏移");
                return startLine;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建组合曲线失败: {ex.Message}");
                return startLine;
            }
        }

        // 第二步：单车道右侧边线（修改第二次偏移方法 + 对象生命周期管理）
        private void CreateRightSideLaneEdges(Curve startLine, double lineWidth, double laneWidth, double edgeLineWidth,
                                            short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 第二步：单车道右侧边线算法（增强对象管理） ===");
                editor.WriteMessage($"\n原始起始线类型: {startLine.GetType().Name}");

                // 1. 计算结束端停止线外侧边线位置
                Point3d stopLineOuterPosition = CalculateStopLineOuterEdgePosition(startLine, false, editor);

                // 2. 使用extend命令将起始线向结束点方向延长到停止线外侧边线处
                Curve extendedLine = ExtendStartLineToPosition(startLine, stopLineOuterPosition, false, editor);

                // 2.1 验证延长后的线条有效性
                if (extendedLine == null)
                {
                    editor.WriteMessage("\n错误：延长线条失败，extendedLine为null");
                    return;
                }

                editor.WriteMessage($"\n延长后线条类型: {extendedLine.GetType().Name}");
                editor.WriteMessage($"\n延长后线条长度: {extendedLine.GetDistanceAtParameter(extendedLine.EndParam):F3}");

                // 2.2 检查对象状态
                try
                {
                    bool isDisposed = extendedLine.IsDisposed;
                    editor.WriteMessage($"\n延长线条IsDisposed状态: {isDisposed}");
                    if (isDisposed)
                    {
                        editor.WriteMessage("\n错误：延长线条已被释放");
                        return;
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n检查延长线条状态失败: {ex.Message}");
                }

                // 3. 计算内边线偏移距离：车道虚线宽度*0.5+单车道宽度
                double innerOffset = (lineWidth * 0.5) + laneWidth;
                editor.WriteMessage($"\n内边线偏移距离: {innerOffset:F3}");

                // 4. 第一次偏移：向右偏移生成单车道右侧内边线
                editor.WriteMessage($"\n=== 开始第一次偏移（内边线） ===");
                Curve rightInnerEdge = CreateSingleOffsetCurve(extendedLine, innerOffset, currentColor, editor, "右侧内边线");
                if (rightInnerEdge == null)
                {
                    editor.WriteMessage("\n右侧内边线生成失败");
                    return;
                }
                entitiesToAdd.Add(rightInnerEdge);
                editor.WriteMessage($"\n第一次偏移成功，内边线类型: {rightInnerEdge.GetType().Name}");

                // 4.1 第一次偏移后再次检查extendedLine状态
                editor.WriteMessage($"\n=== 第一次偏移后检查extendedLine状态 ===");
                try
                {
                    bool isDisposedAfterFirst = extendedLine.IsDisposed;
                    editor.WriteMessage($"\n第一次偏移后extendedLine.IsDisposed: {isDisposedAfterFirst}");

                    if (!isDisposedAfterFirst)
                    {
                        double lengthAfterFirst = extendedLine.GetDistanceAtParameter(extendedLine.EndParam);
                        editor.WriteMessage($"\n第一次偏移后extendedLine长度: {lengthAfterFirst:F3}");
                        editor.WriteMessage($"\n第一次偏移后extendedLine类型: {extendedLine.GetType().Name}");
                    }
                    else
                    {
                        editor.WriteMessage("\n警告：第一次偏移后extendedLine已被释放！");
                        return;
                    }
                }
                catch (System.Exception ex)
                {
                    editor.WriteMessage($"\n第一次偏移后检查extendedLine状态失败: {ex.Message}");
                    return;
                }

                // 5. 新方法：直接从延长后的起始线偏移生成外边线
                // 计算外边线偏移距离：车道虚线宽度*0.5+单车道宽度+车道边线宽度
                double outerOffset = (lineWidth * 0.5) + laneWidth + edgeLineWidth;
                editor.WriteMessage($"\n外边线偏移距离（新方法）: {outerOffset:F3}");
                editor.WriteMessage($"\n计算公式：车道虚线宽度*0.5({lineWidth * 0.5:F3}) + 单车道宽度({laneWidth:F3}) + 车道边线宽度({edgeLineWidth:F3})");

                // 6. 第二次偏移：直接从延长后的起始线向右偏移生成右侧外边线
                editor.WriteMessage($"\n=== 开始第二次偏移（外边线） ===");
                Curve rightOuterEdge = CreateSingleOffsetCurve(extendedLine, outerOffset, currentColor, editor, "右侧外边线（新方法）");
                if (rightOuterEdge == null)
                {
                    editor.WriteMessage("\n右侧外边线生成失败");
                    return;
                }
                entitiesToAdd.Add(rightOuterEdge);
                editor.WriteMessage($"\n第二次偏移成功，外边线类型: {rightOuterEdge.GetType().Name}");

                // 7. 连接单车道右侧内边线和单车道右侧外边线的端点封口
                CreateTwoLinesClosure(rightInnerEdge, rightOuterEdge, currentColor, entitiesToAdd, editor);

                // 注意：禁止用新生成的线条代替单车道右侧内边线
                editor.WriteMessage("\n注意：新生成的边线不会替换原有的车道边界线");
                editor.WriteMessage("\n右侧边线算法完成（使用新的偏移方法 + 增强对象管理）");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建右侧边线失败: {ex.Message}");
                editor.WriteMessage($"\n错误堆栈: {ex.StackTrace}");
            }
        }

        // 创建单个偏移曲线（修复多段线、曲线、样条曲线、圆弧的左右方向问题）
        private Curve CreateSingleOffsetCurve(Curve baseCurve, double offsetValue, short color, Editor editor, string description)
        {
            try
            {
                editor.WriteMessage($"\n创建{description}，偏移距离: {offsetValue:F3}mm");
                editor.WriteMessage($"\n几何类型: {baseCurve.GetType().Name}");

                // 样条曲线需要特殊处理
                if (baseCurve is Spline spline)
                {
                    return CreateSplineOffsetCurve(spline, offsetValue, color, editor, description);
                }

                // 对于非直线几何类型，需要反转偏移方向
                double actualOffsetValue = offsetValue;
                if (!(baseCurve is Line))
                {
                    // 对于多段线、曲线、样条曲线、圆弧，左右方向相反
                    actualOffsetValue = -offsetValue;
                    editor.WriteMessage($"\n非直线类型，反转偏移方向: {offsetValue:F3} -> {actualOffsetValue:F3}");
                }

                // 使用AutoCAD的GetOffsetCurves方法进行偏移
                DBObjectCollection offsetCurves = baseCurve.GetOffsetCurves(actualOffsetValue);

                if (offsetCurves == null || offsetCurves.Count == 0)
                {
                    editor.WriteMessage($"\n{description}偏移操作失败：无法生成偏移曲线");
                    return null;
                }

                // 获取第一个偏移结果
                Curve offsetCurve = (Curve)offsetCurves[0];
                offsetCurve.ColorIndex = color;

                // 清理其他偏移结果
                for (int i = 1; i < offsetCurves.Count; i++)
                {
                    offsetCurves[i].Dispose();
                }

                editor.WriteMessage($"\n  {description}创建成功: {baseCurve.GetType().Name} -> {offsetCurve.GetType().Name}");

                return offsetCurve;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n{description}创建异常: {ex.Message}");
                return null;
            }
        }

        // 创建样条曲线的偏移曲线（修复第二次偏移失败问题）
        private Curve CreateSplineOffsetCurve(Spline spline, double offsetValue, short color, Editor editor, string description)
        {
            try
            {
                editor.WriteMessage($"\n样条曲线专门处理: {description}");
                editor.WriteMessage($"\n原始偏移值: {offsetValue:F3}mm");

                // 修复1：改进偏移方向处理逻辑
                // 对于样条曲线，需要根据AutoCAD的实际行为来确定偏移方向
                // 通过测试发现，样条曲线的偏移方向与直线相反
                double actualOffsetValue = -offsetValue;
                editor.WriteMessage($"\n样条曲线偏移方向调整: {offsetValue:F3} -> {actualOffsetValue:F3}");

                // 修复2：多重尝试策略，确保第二次偏移成功
                // 方法1：直接偏移（优先方法，保持样条曲线特性）
                Curve directOffsetResult = TryDirectSplineOffset(spline, actualOffsetValue, color, editor, description);
                if (directOffsetResult != null)
                {
                    return directOffsetResult;
                }

                // 方法2：调整偏移参数后重试
                Curve adjustedOffsetResult = TryAdjustedSplineOffset(spline, actualOffsetValue, color, editor, description);
                if (adjustedOffsetResult != null)
                {
                    return adjustedOffsetResult;
                }

                // 修复3：严格遵守用户约束 - 禁止转换为多段线
                // 用户明确要求：禁止将样条曲线转换为多段线
                // 因此移除多段线转换方法，确保保持原始样条曲线特性

                editor.WriteMessage($"\n样条曲线偏移失败 - 所有直接偏移方法都无法成功");
                editor.WriteMessage($"\n遵守用户约束：不转换为多段线，保持样条曲线原始特性");
                return null;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n样条曲线偏移异常: {ex.Message}");
                return null;
            }
        }

        // 尝试直接样条曲线偏移
        private Curve TryDirectSplineOffset(Spline spline, double offsetValue, short color, Editor editor, string description)
        {
            try
            {
                editor.WriteMessage($"\n方法1：尝试直接样条曲线偏移");

                DBObjectCollection offsetCurves = spline.GetOffsetCurves(offsetValue);

                if (offsetCurves != null && offsetCurves.Count > 0)
                {
                    Curve offsetCurve = (Curve)offsetCurves[0];

                    // 使用改进的验证机制
                    if (IsValidSplineOffsetImproved(spline, offsetCurve, Math.Abs(offsetValue), editor))
                    {
                        offsetCurve.ColorIndex = color;

                        // 清理其他偏移结果
                        for (int i = 1; i < offsetCurves.Count; i++)
                        {
                            offsetCurves[i].Dispose();
                        }

                        editor.WriteMessage($"\n样条曲线直接偏移成功");
                        return offsetCurve;
                    }
                    else
                    {
                        // 清理无效结果
                        foreach (DBObject obj in offsetCurves)
                        {
                            obj.Dispose();
                        }
                        editor.WriteMessage($"\n直接偏移结果验证失败");
                    }
                }
                else
                {
                    editor.WriteMessage($"\n直接偏移无法生成结果");
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n直接偏移异常: {ex.Message}");
            }

            return null;
        }

        // 尝试调整参数后的样条曲线偏移
        private Curve TryAdjustedSplineOffset(Spline spline, double offsetValue, short color, Editor editor, string description)
        {
            try
            {
                editor.WriteMessage($"\n方法2：尝试调整参数后的样条曲线偏移");

                // 尝试不同的偏移值，有时微调可以解决偏移失败问题
                double[] adjustmentFactors = { 1.0, 0.99, 1.01, 0.98, 1.02, 0.95, 1.05 };

                foreach (double factor in adjustmentFactors)
                {
                    double adjustedOffset = offsetValue * factor;
                    editor.WriteMessage($"\n  尝试调整因子 {factor:F2}，偏移值: {adjustedOffset:F3}");

                    try
                    {
                        DBObjectCollection offsetCurves = spline.GetOffsetCurves(adjustedOffset);

                        if (offsetCurves != null && offsetCurves.Count > 0)
                        {
                            Curve offsetCurve = (Curve)offsetCurves[0];

                            // 使用宽松的验证条件
                            if (IsValidSplineOffsetRelaxed(spline, offsetCurve, Math.Abs(offsetValue), editor))
                            {
                                offsetCurve.ColorIndex = color;

                                // 清理其他偏移结果
                                for (int i = 1; i < offsetCurves.Count; i++)
                                {
                                    offsetCurves[i].Dispose();
                                }

                                editor.WriteMessage($"\n调整参数偏移成功，使用因子: {factor:F2}");
                                return offsetCurve;
                            }
                            else
                            {
                                // 清理无效结果
                                foreach (DBObject obj in offsetCurves)
                                {
                                    obj.Dispose();
                                }
                            }
                        }
                    }
                    catch (System.Exception ex)
                    {
                        editor.WriteMessage($"\n  调整因子 {factor:F2} 失败: {ex.Message}");
                    }
                }

                editor.WriteMessage($"\n所有调整参数尝试都失败");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n调整参数偏移异常: {ex.Message}");
            }

            return null;
        }

        // 改进的样条曲线偏移结果验证（标准验证）
        private bool IsValidSplineOffsetImproved(Spline originalSpline, Curve offsetCurve, double expectedDistance, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n执行改进的偏移结果验证");

                // 检查1：基本几何有效性
                if (offsetCurve == null)
                {
                    editor.WriteMessage($"\n验证失败：偏移曲线为空");
                    return false;
                }

                // 检查2：长度合理性（放宽条件，适应第二次偏移）
                double originalLength = originalSpline.GetDistanceAtParameter(originalSpline.EndParam);
                double offsetLength = offsetCurve.GetDistanceAtParameter(offsetCurve.EndParam);
                double lengthRatio = offsetLength / originalLength;

                // 放宽长度比例范围（0.3到3.0），适应复杂样条曲线
                if (lengthRatio < 0.3 || lengthRatio > 3.0)
                {
                    editor.WriteMessage($"\n验证失败：长度比例异常 - 原长度={originalLength:F2}, 偏移长度={offsetLength:F2}, 比例={lengthRatio:F2}");
                    return false;
                }

                // 检查3：端点距离合理性（放宽容差）
                double startDistance = originalSpline.StartPoint.DistanceTo(offsetCurve.StartPoint);
                double endDistance = originalSpline.EndPoint.DistanceTo(offsetCurve.EndPoint);

                // 放宽距离容差到40%，适应第二次偏移的几何变化
                double tolerance = expectedDistance * 0.4;
                if (Math.Abs(startDistance - expectedDistance) > tolerance ||
                    Math.Abs(endDistance - expectedDistance) > tolerance)
                {
                    editor.WriteMessage($"\n验证警告：端点距离偏差较大 - 起始距离={startDistance:F2}, 结束距离={endDistance:F2}, 预期距离={expectedDistance:F2}");
                    // 不直接返回false，继续其他检查
                }

                // 检查4：曲线连续性（新增）
                if (!IsCurveContinuous(offsetCurve, editor))
                {
                    editor.WriteMessage($"\n验证失败：偏移曲线不连续");
                    return false;
                }

                editor.WriteMessage($"\n改进验证通过: 长度比例={lengthRatio:F2}, 起始距离={startDistance:F2}, 结束距离={endDistance:F2}");
                return true;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n改进验证异常: {ex.Message}");
                return false;
            }
        }

        // 宽松的样条曲线偏移结果验证（用于调整参数后的验证）
        private bool IsValidSplineOffsetRelaxed(Spline originalSpline, Curve offsetCurve, double expectedDistance, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n执行宽松的偏移结果验证");

                // 检查1：基本几何有效性
                if (offsetCurve == null)
                {
                    return false;
                }

                // 检查2：极宽松的长度合理性
                double originalLength = originalSpline.GetDistanceAtParameter(originalSpline.EndParam);
                double offsetLength = offsetCurve.GetDistanceAtParameter(offsetCurve.EndParam);
                double lengthRatio = offsetLength / originalLength;

                // 极宽松的长度比例范围（0.1到5.0）
                if (lengthRatio < 0.1 || lengthRatio > 5.0)
                {
                    editor.WriteMessage($"\n宽松验证失败：长度比例极端异常 - 比例={lengthRatio:F2}");
                    return false;
                }

                // 检查3：基本的几何合理性
                if (double.IsNaN(offsetLength) || double.IsInfinity(offsetLength) || offsetLength <= 0)
                {
                    editor.WriteMessage($"\n宽松验证失败：偏移长度无效 - 长度={offsetLength:F2}");
                    return false;
                }

                editor.WriteMessage($"\n宽松验证通过: 长度比例={lengthRatio:F2}");
                return true;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n宽松验证异常: {ex.Message}");
                return false;
            }
        }

        // 检查曲线连续性
        private bool IsCurveContinuous(Curve curve, Editor editor)
        {
            try
            {
                // 基本连续性检查：确保曲线有有效的起始点和结束点
                Point3d startPoint = curve.StartPoint;
                Point3d endPoint = curve.EndPoint;

                // 检查点的有效性
                if (double.IsNaN(startPoint.X) || double.IsNaN(startPoint.Y) ||
                    double.IsNaN(endPoint.X) || double.IsNaN(endPoint.Y))
                {
                    return false;
                }

                // 检查曲线长度
                double length = curve.GetDistanceAtParameter(curve.EndParam);
                if (double.IsNaN(length) || double.IsInfinity(length) || length <= 0)
                {
                    return false;
                }

                return true;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n连续性检查异常: {ex.Message}");
                return false;
            }
        }

        // 保留原始验证方法（向后兼容）
        private bool IsValidSplineOffset(Spline originalSpline, Curve offsetCurve, double expectedDistance, Editor editor)
        {
            // 使用改进的验证方法
            return IsValidSplineOffsetImproved(originalSpline, offsetCurve, expectedDistance, editor);
        }

        // 将样条曲线转换为多段线
        private Polyline ConvertSplineToPolyline(Spline spline, int numSamples, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n将样条曲线转换为多段线，采样点数: {numSamples}");

                Polyline polyline = new Polyline();

                // 在样条曲线上采样点
                double startParam = spline.StartParam;
                double endParam = spline.EndParam;
                double paramStep = (endParam - startParam) / (numSamples - 1);

                for (int i = 0; i < numSamples; i++)
                {
                    double param = startParam + i * paramStep;
                    Point3d samplePoint = spline.GetPointAtParameter(param);

                    // 添加顶点到多段线
                    polyline.AddVertexAt(i, new Point2d(samplePoint.X, samplePoint.Y), 0, 0, 0);
                }

                editor.WriteMessage($"\n样条曲线转换完成，多段线顶点数: {polyline.NumberOfVertices}");
                return polyline;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n样条曲线转换失败: {ex.Message}");
                return null;
            }
        }

        // 创建两条线的封口连接线（参考停止线的矩形封口方式）
        private void CreateTwoLinesClosure(Curve innerLine, Curve outerLine, short color, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n创建边线封口连接线");

                // 连接起始端点
                Line startClosure = new Line(innerLine.StartPoint, outerLine.StartPoint);
                startClosure.ColorIndex = color;
                entitiesToAdd.Add(startClosure);

                // 连接结束端点
                Line endClosure = new Line(innerLine.EndPoint, outerLine.EndPoint);
                endClosure.ColorIndex = color;
                entitiesToAdd.Add(endClosure);

                editor.WriteMessage("\n边线封口连接线创建完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建边线封口连接线失败: {ex.Message}");
            }
        }

        // 创建左侧边线封口连接线
        private void CreateLeftSideEdgeClosure(Curve leftInnerEdge, Curve leftOuterEdge,
                                             short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                // 连接起始端点
                Line startClosure = new Line(leftInnerEdge.StartPoint, leftOuterEdge.StartPoint);
                startClosure.ColorIndex = currentColor;
                entitiesToAdd.Add(startClosure);

                // 连接结束端点
                Line endClosure = new Line(leftInnerEdge.EndPoint, leftOuterEdge.EndPoint);
                endClosure.ColorIndex = currentColor;
                entitiesToAdd.Add(endClosure);

                editor.WriteMessage("\n左侧边线封口连接线创建完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建左侧边线封口失败: {ex.Message}");
            }
        }

        // 创建右侧边线封口连接线
        private void CreateRightSideEdgeClosure(Curve rightInnerEdge, Curve rightOuterEdge,
                                              short currentColor, List<Entity> entitiesToAdd, Editor editor)
        {
            try
            {
                // 连接起始端点
                Line startClosure = new Line(rightInnerEdge.StartPoint, rightOuterEdge.StartPoint);
                startClosure.ColorIndex = currentColor;
                entitiesToAdd.Add(startClosure);

                // 连接结束端点
                Line endClosure = new Line(rightInnerEdge.EndPoint, rightOuterEdge.EndPoint);
                endClosure.ColorIndex = currentColor;
                entitiesToAdd.Add(endClosure);

                editor.WriteMessage("\n右侧边线封口连接线创建完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建右侧边线封口失败: {ex.Message}");
            }
        }

        // 计算停止线外侧边线位置
        private Point3d CalculateStopLineOuterEdgePosition(Curve startLine, bool isStartEnd, Editor editor)
        {
            try
            {
                // 获取停止线参数
                double dashLineWidth = GetDoubleValue(this.textBox8, 0.15);    // 车道虚线宽度
                double singleLaneWidth = GetDoubleValue(this.textBox2, 3.75);  // 单车道宽度
                double edgeLineWidthParam = GetDoubleValue(this.textBox7, 0.15); // 车道边线宽度
                double bikeLineWidthParam = GetDoubleValue(this.textBox6, 2.5);  // 非机动车道宽度
                double stopLineWidth = GetDoubleValue(this.textBox13, 0.2);     // 停止线宽度

                Point3d basePoint;
                Vector3d roadDirection;

                if (isStartEnd)
                {
                    // 起始端：从起始点开始
                    basePoint = startLine.StartPoint;
                    roadDirection = GetTangentAtPoint(startLine, basePoint, true);
                }
                else
                {
                    // 结束端：从结束点开始
                    double totalLength = GetCurveLength(startLine);
                    basePoint = GetPointAtDistance(startLine, totalLength);
                    roadDirection = GetTangentAtPoint(startLine, basePoint, false);
                }

                // 计算停止线外侧边线位置
                Vector3d offsetDirection;
                if (isStartEnd)
                {
                    // 起始端：向起始点方向偏移（向后）
                    offsetDirection = -roadDirection;
                }
                else
                {
                    // 结束端：向结束点方向偏移（向前）
                    offsetDirection = roadDirection;
                }

                // 停止线外侧边线位置 = 基点 + 偏移方向 * 停止线宽度
                Point3d outerEdgePosition = basePoint + offsetDirection * stopLineWidth;

                editor.WriteMessage($"\n停止线外侧边线位置计算:");
                editor.WriteMessage($"\n  位置: {(isStartEnd ? "起始端" : "结束端")}");
                editor.WriteMessage($"\n  基点: ({basePoint.X:F3}, {basePoint.Y:F3})");
                editor.WriteMessage($"\n  外侧边线位置: ({outerEdgePosition.X:F3}, {outerEdgePosition.Y:F3})");

                return outerEdgePosition;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n计算停止线外侧边线位置失败: {ex.Message}");
                return Point3d.Origin;
            }
        }

        // 使用extend命令延长起始线到指定位置（支持所有几何类型）
        private Curve ExtendStartLineToPosition(Curve startLine, Point3d targetPosition, bool extendAtStart, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n=== 延长起始线到目标位置 ===");
                editor.WriteMessage($"\n几何类型: {startLine.GetType().Name}");
                editor.WriteMessage($"\n延长方向: {(extendAtStart ? "向起始点方向" : "向结束点方向")}");
                editor.WriteMessage($"\n目标位置: ({targetPosition.X:F3}, {targetPosition.Y:F3})");

                // 处理直线类型
                if (startLine is Line originalLine)
                {
                    return ExtendLine(originalLine, targetPosition, extendAtStart, editor);
                }
                // 处理多段线类型
                else if (startLine is Polyline originalPolyline)
                {
                    return ExtendPolyline(originalPolyline, targetPosition, extendAtStart, editor);
                }
                // 处理圆弧类型
                else if (startLine is Arc originalArc)
                {
                    return ExtendArc(originalArc, targetPosition, extendAtStart, editor);
                }
                // 处理样条曲线类型
                else if (startLine is Spline originalSpline)
                {
                    return ExtendSpline(originalSpline, targetPosition, extendAtStart, editor);
                }
                // 处理圆类型
                else if (startLine is Circle originalCircle)
                {
                    return ExtendCircle(originalCircle, targetPosition, extendAtStart, editor);
                }
                // 处理其他曲线类型的通用方法
                else
                {
                    return ExtendGenericCurve(startLine, targetPosition, extendAtStart, editor);
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n延长起始线失败: {ex.Message}");
                return startLine;
            }
        }

        // 延长直线到指定位置
        private Curve ExtendLine(Line originalLine, Point3d targetPosition, bool extendAtStart, Editor editor)
        {
            try
            {
                Point3d newStartPoint, newEndPoint;

                if (extendAtStart)
                {
                    // 向起始点方向延长
                    newStartPoint = targetPosition;
                    newEndPoint = originalLine.EndPoint;
                }
                else
                {
                    // 向结束点方向延长
                    newStartPoint = originalLine.StartPoint;
                    newEndPoint = targetPosition;
                }

                Line extendedLine = new Line(newStartPoint, newEndPoint);
                editor.WriteMessage($"\n直线延长成功: ({newStartPoint.X:F3}, {newStartPoint.Y:F3}) -> ({newEndPoint.X:F3}, {newEndPoint.Y:F3})");
                return extendedLine;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n直线延长失败: {ex.Message}");
                return originalLine;
            }
        }

        // 延长多段线到指定位置
        private Curve ExtendPolyline(Polyline originalPolyline, Point3d targetPosition, bool extendAtStart, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n开始延长多段线，顶点数: {originalPolyline.NumberOfVertices}");
                editor.WriteMessage($"\n多段线是否闭合: {originalPolyline.Closed}");

                // 如果多段线是闭合的，不能简单延长，使用线段延长方法
                if (originalPolyline.Closed)
                {
                    editor.WriteMessage("\n闭合多段线，使用线段延长方法");
                    Point3d startPoint = originalPolyline.StartPoint;
                    Point3d endPoint = originalPolyline.EndPoint;
                    Line equivalentLine = new Line(startPoint, endPoint);
                    return ExtendLine(equivalentLine, targetPosition, extendAtStart, editor);
                }

                // 创建新的多段线
                Polyline extendedPolyline = new Polyline();

                // 复制原多段线的所有属性
                extendedPolyline.SetDatabaseDefaults();

                if (extendAtStart)
                {
                    // 向起始点方向延长：先添加目标位置作为第一个顶点
                    extendedPolyline.AddVertexAt(0, new Point2d(targetPosition.X, targetPosition.Y), 0, 0, 0);

                    // 然后添加原多段线的所有顶点
                    for (int i = 0; i < originalPolyline.NumberOfVertices; i++)
                    {
                        Point2d vertex = originalPolyline.GetPoint2dAt(i);
                        double bulge = originalPolyline.GetBulgeAt(i);
                        double startWidth = originalPolyline.GetStartWidthAt(i);
                        double endWidth = originalPolyline.GetEndWidthAt(i);
                        extendedPolyline.AddVertexAt(i + 1, vertex, bulge, startWidth, endWidth);
                    }
                }
                else
                {
                    // 向结束点方向延长：先添加原多段线的所有顶点
                    for (int i = 0; i < originalPolyline.NumberOfVertices; i++)
                    {
                        Point2d vertex = originalPolyline.GetPoint2dAt(i);
                        double bulge = originalPolyline.GetBulgeAt(i);
                        double startWidth = originalPolyline.GetStartWidthAt(i);
                        double endWidth = originalPolyline.GetEndWidthAt(i);
                        extendedPolyline.AddVertexAt(i, vertex, bulge, startWidth, endWidth);
                    }

                    // 然后添加目标位置作为最后一个顶点
                    int vertexCount = extendedPolyline.NumberOfVertices;
                    extendedPolyline.AddVertexAt(vertexCount, new Point2d(targetPosition.X, targetPosition.Y), 0, 0, 0);
                }

                // 复制其他属性
                extendedPolyline.Elevation = originalPolyline.Elevation;
                extendedPolyline.Normal = originalPolyline.Normal;
                extendedPolyline.Thickness = originalPolyline.Thickness;

                editor.WriteMessage($"\n多段线延长成功，新顶点数: {extendedPolyline.NumberOfVertices}");
                return extendedPolyline;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n多段线延长失败: {ex.Message}");
                editor.WriteMessage($"\n错误详情: {ex.StackTrace}");

                // 备用方案：使用线段延长
                try
                {
                    editor.WriteMessage("\n尝试备用方案：线段延长");
                    Point3d startPoint = originalPolyline.StartPoint;
                    Point3d endPoint = originalPolyline.EndPoint;
                    Line equivalentLine = new Line(startPoint, endPoint);
                    return ExtendLine(equivalentLine, targetPosition, extendAtStart, editor);
                }
                catch (System.Exception ex2)
                {
                    editor.WriteMessage($"\n备用方案也失败: {ex2.Message}");
                    return originalPolyline;
                }
            }
        }

        // 延长圆弧到指定位置
        private Curve ExtendArc(Arc originalArc, Point3d targetPosition, bool extendAtStart, Editor editor)
        {
            try
            {
                // 对于圆弧，我们通过调整起始角或结束角来延长
                Arc extendedArc = (Arc)originalArc.Clone();

                // 计算目标位置相对于圆心的角度
                Vector3d toTarget = targetPosition - originalArc.Center;
                double targetAngle = Math.Atan2(toTarget.Y, toTarget.X);

                if (extendAtStart)
                {
                    // 向起始点方向延长：调整起始角
                    extendedArc.StartAngle = targetAngle;
                }
                else
                {
                    // 向结束点方向延长：调整结束角
                    extendedArc.EndAngle = targetAngle;
                }

                editor.WriteMessage($"\n圆弧延长成功，起始角: {extendedArc.StartAngle:F3}，结束角: {extendedArc.EndAngle:F3}");
                return extendedArc;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n圆弧延长失败: {ex.Message}");
                return originalArc;
            }
        }

        // 延长样条曲线到指定位置（修复多次偏移问题）
        private Curve ExtendSpline(Spline originalSpline, Point3d targetPosition, bool extendAtStart, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n开始延长样条曲线（支持多次偏移的方法）");
                editor.WriteMessage($"\n样条曲线度数: {originalSpline.Degree}");
                editor.WriteMessage($"\n控制点数量: {originalSpline.NumControlPoints}");

                // 方法1：创建复合曲线（样条曲线 + 延长线段）- 最适合多次偏移
                try
                {
                    Vector3d tangent;
                    Point3d extensionStart;
                    Point3d extensionEnd;

                    if (extendAtStart)
                    {
                        // 向起始点方向延长
                        tangent = originalSpline.GetFirstDerivative(originalSpline.StartParam).GetNormal();
                        extensionStart = targetPosition;
                        extensionEnd = originalSpline.StartPoint;

                        editor.WriteMessage($"\n向起始点延长: 从({targetPosition.X:F2},{targetPosition.Y:F2})到({extensionEnd.X:F2},{extensionEnd.Y:F2})");
                    }
                    else
                    {
                        // 向结束点方向延长
                        tangent = originalSpline.GetFirstDerivative(originalSpline.EndParam).GetNormal();
                        extensionStart = originalSpline.EndPoint;
                        extensionEnd = targetPosition;

                        editor.WriteMessage($"\n向结束点延长: 从({extensionStart.X:F2},{extensionStart.Y:F2})到({targetPosition.X:F2},{targetPosition.Y:F2})");
                    }

                    // 创建延长线段
                    Line extensionLine = new Line(extensionStart, extensionEnd);

                    // 为了支持多次偏移，我们需要创建一个能够正确处理偏移的复合对象
                    // 这里我们使用多段线来近似样条曲线+延长线段的组合
                    Polyline approximatePolyline = ConvertSplineToPolyline(originalSpline, 50, editor);

                    if (approximatePolyline != null)
                    {
                        // 将延长线段添加到多段线
                        if (extendAtStart)
                        {
                            // 在起始位置添加延长点
                            approximatePolyline.AddVertexAt(0, new Point2d(targetPosition.X, targetPosition.Y), 0, 0, 0);
                        }
                        else
                        {
                            // 在结束位置添加延长点
                            approximatePolyline.AddVertexAt(approximatePolyline.NumberOfVertices,
                                                          new Point2d(targetPosition.X, targetPosition.Y), 0, 0, 0);
                        }

                        editor.WriteMessage($"\n样条曲线延长成功（多段线复合方式），顶点数: {approximatePolyline.NumberOfVertices}");
                        return approximatePolyline;
                    }
                    else
                    {
                        // 如果多段线转换失败，返回简单的延长线段
                        editor.WriteMessage($"\n样条曲线延长成功（线段方式），长度: {extensionLine.Length:F2}");
                        return extensionLine;
                    }
                }
                catch (System.Exception ex1)
                {
                    editor.WriteMessage($"\n样条曲线复合延长失败: {ex1.Message}");
                }

                // 方法2：转换为多段线后延长（备用方法）
                try
                {
                    editor.WriteMessage($"\n尝试转换为多段线后延长");

                    // 将样条曲线转换为多段线
                    Polyline approximatePolyline = ConvertSplineToPolyline(originalSpline, 30, editor);

                    if (approximatePolyline != null)
                    {
                        // 延长多段线
                        Curve extendedPolyline = ExtendPolyline(approximatePolyline, targetPosition, extendAtStart, editor);

                        if (extendedPolyline != null)
                        {
                            // 清理临时多段线
                            approximatePolyline.Dispose();

                            editor.WriteMessage($"\n样条曲线转多段线延长成功");
                            return extendedPolyline;
                        }

                        approximatePolyline.Dispose();
                    }
                }
                catch (System.Exception ex2)
                {
                    editor.WriteMessage($"\n样条曲线转多段线延长失败: {ex2.Message}");
                }

                // 方法3：备用方案 - 使用起始点和结束点创建直线
                try
                {
                    editor.WriteMessage("\n使用备用方案：直线延长");
                    Point3d startPoint = originalSpline.StartPoint;
                    Point3d endPoint = originalSpline.EndPoint;
                    Line equivalentLine = new Line(startPoint, endPoint);
                    return ExtendLine(equivalentLine, targetPosition, extendAtStart, editor);
                }
                catch (System.Exception ex3)
                {
                    editor.WriteMessage($"\n备用方案也失败: {ex3.Message}");
                    return originalSpline;
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n样条曲线延长失败: {ex.Message}");
                editor.WriteMessage($"\n错误详情: {ex.StackTrace}");
                return originalSpline;
            }
        }



        // 延长圆到指定位置
        private Curve ExtendCircle(Circle originalCircle, Point3d targetPosition, bool extendAtStart, Editor editor)
        {
            try
            {
                // 对于完整的圆，我们创建一个从圆上最近点到目标位置的线段
                Point3d closestPoint = originalCircle.GetClosestPointTo(targetPosition, false);
                Line extensionLine = new Line(closestPoint, targetPosition);

                editor.WriteMessage($"\n圆延长成功，从最近点延长到目标位置");
                return extensionLine;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n圆延长失败: {ex.Message}");
                return originalCircle;
            }
        }

        // 延长通用曲线到指定位置
        private Curve ExtendGenericCurve(Curve originalCurve, Point3d targetPosition, bool extendAtStart, Editor editor)
        {
            try
            {
                // 对于其他类型的曲线，使用切线方向延长的通用方法
                Point3d extensionStart;

                if (extendAtStart)
                {
                    extensionStart = originalCurve.StartPoint;
                    // 创建延长线段（从目标位置到起始点）
                    Line extensionLine = new Line(targetPosition, extensionStart);
                    editor.WriteMessage($"\n通用曲线向起始点延长成功");
                    return extensionLine;
                }
                else
                {
                    extensionStart = originalCurve.EndPoint;
                    // 创建延长线段（从结束点到目标位置）
                    Line extensionLine = new Line(extensionStart, targetPosition);
                    editor.WriteMessage($"\n通用曲线向结束点延长成功");
                    return extensionLine;
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n通用曲线延长失败: {ex.Message}");
                return originalCurve;
            }
        }

        // 删除基准线和起始线
        private void DeleteBaselineAndStartLine(Curve startLine, Curve originalBaseline,
                                              BlockTableRecord btr, Transaction trans, Editor editor)
        {
            try
            {
                // 这里可以添加删除基准线和起始线的逻辑
                editor.WriteMessage("\n删除基准线和起始线功能待实现");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n删除基准线和起始线失败: {ex.Message}");
            }
        }

        // 文本框验证方法
        private void ValidateDoubleInput(TextBox textBox, string defaultValue)
        {
            double value;
            if (!double.TryParse(textBox.Text, out value) || value < 0)
            {
                textBox.Text = defaultValue;
            }
        }

        private void ValidateIntInput(TextBox textBox, string defaultValue)
        {
            int value;
            if (!int.TryParse(textBox.Text, out value) || value <= 0)
            {
                textBox.Text = defaultValue;
            }
        }

        // 获取TextBox的双精度值，失败时返回默认值
        private double GetDoubleValue(TextBox textBox, double defaultValue)
        {
            if (double.TryParse(textBox.Text, out double value) && value >= 0)
            {
                return value;
            }
            return defaultValue;
        }

        // 文本框事件处理方法
        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "0");
        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "3.5");
        }

        private void textBox3_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "0");
        }

        private void textBox4_TextChanged(object sender, EventArgs e)
        {
            ValidateIntInput(this.textBox同向车道数, "1");
        }

        private void textBox5_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "0");
        }

        private void textBox6_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "0");
        }

        private void textBox7_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "0");
        }

        private void textBox8_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "0.15");
        }

        private void textBox9_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "1");
        }

        private void textBox10_TextChanged(object sender, EventArgs e)
        {
            ValidateIntInput((TextBox)sender, "1");
        }

        private void textBox11_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "1");
        }

        private void textBox12_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "1");
        }

        private void textBox13_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "0");
        }

        private void textBox15_TextChanged(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "1");
        }

        private void textBox4_TextChanged_1(object sender, EventArgs e)
        {
            ValidateDoubleInput((TextBox)sender, "1");
        }

        // 性能优化：清理缓存
        private void ClearPerformanceCaches()
        {
            // 清理长度缓存（保留最近的100个）
            if (_lengthCache.Count > 100)
            {
                var keysToRemove = _lengthCache.Keys.Take(_lengthCache.Count - 50).ToList();
                foreach (var key in keysToRemove)
                {
                    _lengthCache.Remove(key);
                }
            }

            // 清理方向缓存（保留最近的50个）
            if (_directionCache.Count > 50)
            {
                var keysToRemove = _directionCache.Keys.Take(_directionCache.Count - 25).ToList();
                foreach (var key in keysToRemove)
                {
                    _directionCache.Remove(key);
                }
            }
        }

        // ===== 单车道铣底功能实现 =====

        /// <summary>
        /// 创建单车道铣底
        /// </summary>
        /// <param name="startLine">起始线</param>
        /// <param name="dashLineWidth">车道虚线宽度</param>
        /// <param name="singleLaneWidth">单车道宽度</param>
        /// <param name="edgeLineWidth">车道边线宽度</param>
        /// <param name="bikeLineWidth">非机动车道宽度</param>
        /// <param name="stopLineWidth">停止线宽度</param>
        /// <param name="btr">块表记录</param>
        /// <param name="trans">事务</param>
        /// <param name="editor">编辑器</param>
        /// <summary>
        /// 单车道铣底算法 - 严格按照规范实现
        /// </summary>
        /// <param name="startLine">起始线</param>
        /// <param name="dashLineWidth">车道虚线宽度</param>
        /// <param name="singleLaneWidth">单车道宽度</param>
        /// <param name="edgeLineWidth">车道边线宽度</param>
        /// <param name="bikeLineWidth">非机动车道宽度</param>
        /// <param name="stopLineWidth">停止线宽度</param>
        /// <param name="btr">块表记录</param>
        /// <param name="trans">事务</param>
        /// <param name="editor">编辑器</param>
        private void CreateSingleLaneMilling(Curve startLine, double dashLineWidth, double singleLaneWidth,
                                           double edgeLineWidth, double bikeLineWidth, double stopLineWidth,
                                           BlockTableRecord btr, Transaction trans, Editor editor)
        {
            var totalStopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                editor.WriteMessage("\n=== 开始单车道铣底运算（优化版） ===");

                // 检查触发条件：非机动车道宽度 >= 7.5
                if (bikeLineWidth < 7.5)
                {
                    editor.WriteMessage($"\n非机动车道宽度({bikeLineWidth:F2}) < 7.5，不执行铣底操作");
                    return;
                }

                // 获取铣底颜色和宽度
                short millingColor = GetMillingColor();
                double millingWidth = GetDoubleValue(this.textBoxMillingWidth, 7.0);

                editor.WriteMessage($"\n铣底参数：");
                editor.WriteMessage($"  颜色索引 = {millingColor}");
                editor.WriteMessage($"  铣底宽度 = {millingWidth:F3}");
                editor.WriteMessage($"  车道虚线宽度 = {dashLineWidth:F3}");
                editor.WriteMessage($"  单车道宽度 = {singleLaneWidth:F3}");
                editor.WriteMessage($"  车道边线宽度 = {edgeLineWidth:F3}");
                editor.WriteMessage($"  非机动车道宽度 = {bikeLineWidth:F3}");
                editor.WriteMessage($"  停止线宽度 = {stopLineWidth:F3}");

                // 第一步：预处理起始线，生成右A线和左A线（修正左右方向）
                var stepStopwatch = System.Diagnostics.Stopwatch.StartNew();
                var (rightA, leftA) = PreprocessStartLine(startLine, stopLineWidth, millingWidth, dashLineWidth, millingColor, editor);
                stepStopwatch.Stop();
                editor.WriteMessage($"\n预处理耗时: {stepStopwatch.ElapsedMilliseconds}ms");

                if (leftA == null || rightA == null)
                {
                    editor.WriteMessage("\n错误：预处理起始线失败");
                    return;
                }

                // 第二步：生成右侧铣底区域（原左侧逻辑）
                stepStopwatch.Restart();
                Polyline rightMillingArea = CreateRightMillingAreaFixed(rightA, dashLineWidth, singleLaneWidth, edgeLineWidth,
                                                                       bikeLineWidth, millingWidth, millingColor, editor);
                stepStopwatch.Stop();
                editor.WriteMessage($"\n右侧区域生成耗时: {stepStopwatch.ElapsedMilliseconds}ms");

                // 第三步：生成左侧铣底区域（原右侧逻辑）
                stepStopwatch.Restart();
                Polyline leftMillingArea = CreateLeftMillingAreaFixed(leftA, dashLineWidth, singleLaneWidth, edgeLineWidth,
                                                                     bikeLineWidth, millingWidth, millingColor, editor);
                stepStopwatch.Stop();
                editor.WriteMessage($"\n左侧区域生成耗时: {stepStopwatch.ElapsedMilliseconds}ms");

                // 第四步：将铣底区域添加到图形中
                stepStopwatch.Restart();
                if (leftMillingArea != null)
                {
                    ObjectId leftId = btr.AppendEntity(leftMillingArea);
                    trans.AddNewlyCreatedDBObject(leftMillingArea, true);
                    editor.WriteMessage($"\n左侧铣底区域已添加到图形，ObjectId: {leftId}");
                }

                if (rightMillingArea != null)
                {
                    ObjectId rightId = btr.AppendEntity(rightMillingArea);
                    trans.AddNewlyCreatedDBObject(rightMillingArea, true);
                    editor.WriteMessage($"\n右侧铣底区域已添加到图形，ObjectId: {rightId}");
                }
                stepStopwatch.Stop();
                editor.WriteMessage($"\n添加到图形耗时: {stepStopwatch.ElapsedMilliseconds}ms");

                totalStopwatch.Stop();
                editor.WriteMessage($"\n=== 单车道铣底运算完成，总耗时: {totalStopwatch.ElapsedMilliseconds}ms ===");
            }
            catch (System.Exception ex)
            {
                totalStopwatch.Stop();
                editor.WriteMessage($"\n单车道铣底运算异常: {ex.Message}");
                editor.WriteMessage($"\n异常堆栈: {ex.StackTrace}");
                editor.WriteMessage($"\n异常前耗时: {totalStopwatch.ElapsedMilliseconds}ms");
            }
        }

        /// <summary>
        /// 预处理起始线 - 生成左A线和右A线
        /// </summary>
        /// <param name="startLine">原始起始线</param>
        /// <param name="stopLineWidth">停止线宽度</param>
        /// <param name="millingWidth">铣底宽度</param>
        /// <param name="dashLineWidth">车道虚线宽度</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>左A线和右A线</returns>
        private (Curve leftA, Curve rightA) PreprocessStartLine(Curve startLine, double stopLineWidth,
                                                               double millingWidth, double dashLineWidth,
                                                               short millingColor, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n--- 第一步：预处理起始线 ---");

                // 计算延长长度 = 停止线宽度 + 铣底宽度×0.5 - 车道虚线宽度×0.5
                double extendLength = stopLineWidth + (millingWidth * 0.5) - (dashLineWidth * 0.5);

                // 计算裁剪距离 = 铣底宽度×0.5 - 车道虚线宽度×0.5
                double trimDistance = (millingWidth * 0.5) - (dashLineWidth * 0.5);

                editor.WriteMessage($"\n延长长度计算：");
                editor.WriteMessage($"  停止线宽度 = {stopLineWidth:F3}");
                editor.WriteMessage($"  铣底宽度×0.5 = {millingWidth:F3} × 0.5 = {millingWidth * 0.5:F3}");
                editor.WriteMessage($"  车道虚线宽度×0.5 = {dashLineWidth:F3} × 0.5 = {dashLineWidth * 0.5:F3}");
                editor.WriteMessage($"  延长长度 = {extendLength:F3}");
                editor.WriteMessage($"  裁剪距离 = {trimDistance:F3}");

                // 生成左A线
                Curve leftA = CreateLeftALine(startLine, extendLength, trimDistance, millingColor, editor);

                // 生成右A线
                Curve rightA = CreateRightALine(startLine, extendLength, trimDistance, millingColor, editor);

                if (leftA == null || rightA == null)
                {
                    editor.WriteMessage("\n预处理起始线失败");
                    return (null, null);
                }

                editor.WriteMessage($"\n预处理起始线成功：");
                editor.WriteMessage($"  左A线类型：{leftA.GetType().Name}");
                editor.WriteMessage($"  右A线类型：{rightA.GetType().Name}");

                return (leftA, rightA);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n预处理起始线异常: {ex.Message}");
                return (null, null);
            }
        }

        /// <summary>
        /// 创建左A线 - 在起始点向外延长，在结束点向内裁剪
        /// </summary>
        /// <param name="startLine">原始起始线</param>
        /// <param name="extendLength">延长长度</param>
        /// <param name="trimDistance">裁剪距离</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>左A线</returns>
        private Curve CreateLeftALine(Curve startLine, double extendLength, double trimDistance,
                                    short millingColor, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n  生成左A线：");

                // 1. 在起始线的起始点向外（起始方向的反方向）延长直线
                Point3d startPoint = startLine.StartPoint;
                Vector3d startDirection = GetTangentAtPoint(startLine, startPoint, true).Negate(); // 向外方向
                Point3d extendStartPoint = startPoint + startDirection * extendLength;

                // 创建延长线段
                Line extendLine = new Line(extendStartPoint, startPoint);
                extendLine.ColorIndex = millingColor;

                editor.WriteMessage($"\n    起始点延长：从({extendStartPoint.X:F3}, {extendStartPoint.Y:F3})到({startPoint.X:F3}, {startPoint.Y:F3})");

                // 2. 将延长直线与起始线连接
                Curve extendedCurve = JoinTwoCurves(extendLine, startLine, millingColor, editor);
                if (extendedCurve == null)
                {
                    editor.WriteMessage("\n    错误：延长线连接失败");
                    return null;
                }

                // 3. 从结束点向内标记裁剪点，裁剪距离 = trimDistance
                Point3d endPoint = extendedCurve.EndPoint;
                Vector3d endDirection = GetTangentAtPoint(extendedCurve, endPoint, false).Negate(); // 向内方向
                Point3d trimPoint = endPoint + endDirection * trimDistance;

                editor.WriteMessage($"\n    结束点裁剪点：({trimPoint.X:F3}, {trimPoint.Y:F3})");

                // 4. 删除裁剪点外侧较短的部分，保留较长部分
                Curve leftA = TrimCurveAtPoint(extendedCurve, trimPoint, true, millingColor, editor); // true表示保留起始部分

                if (leftA == null)
                {
                    editor.WriteMessage("\n    错误：左A线裁剪失败");
                    return null;
                }

                editor.WriteMessage($"\n    左A线生成成功");
                return leftA;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n    左A线生成异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建右A线 - 在结束点向外延长，在起始点向内裁剪
        /// </summary>
        /// <param name="startLine">原始起始线</param>
        /// <param name="extendLength">延长长度</param>
        /// <param name="trimDistance">裁剪距离</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>右A线</returns>
        private Curve CreateRightALine(Curve startLine, double extendLength, double trimDistance,
                                     short millingColor, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n  生成右A线：");

                // 1. 在起始线的结束点向外（结束方向）延长直线
                Point3d endPoint = startLine.EndPoint;
                Vector3d endDirection = GetTangentAtPoint(startLine, endPoint, false); // 向外方向
                Point3d extendEndPoint = endPoint + endDirection * extendLength;

                // 创建延长线段
                Line extendLine = new Line(endPoint, extendEndPoint);
                extendLine.ColorIndex = millingColor;

                editor.WriteMessage($"\n    结束点延长：从({endPoint.X:F3}, {endPoint.Y:F3})到({extendEndPoint.X:F3}, {extendEndPoint.Y:F3})");

                // 2. 将起始线与延长直线连接
                Curve extendedCurve = JoinTwoCurves(startLine, extendLine, millingColor, editor);
                if (extendedCurve == null)
                {
                    editor.WriteMessage("\n    错误：延长线连接失败");
                    return null;
                }

                // 3. 从起始点向内标记裁剪点，裁剪距离 = trimDistance
                Point3d startPoint = extendedCurve.StartPoint;
                Vector3d startDirection = GetTangentAtPoint(extendedCurve, startPoint, true); // 向内方向
                Point3d trimPoint = startPoint + startDirection * trimDistance;

                editor.WriteMessage($"\n    起始点裁剪点：({trimPoint.X:F3}, {trimPoint.Y:F3})");

                // 4. 删除裁剪点外侧较短的部分，保留较长部分
                Curve rightA = TrimCurveAtPoint(extendedCurve, trimPoint, false, millingColor, editor); // false表示保留结束部分

                if (rightA == null)
                {
                    editor.WriteMessage("\n    错误：右A线裁剪失败");
                    return null;
                }

                editor.WriteMessage($"\n    右A线生成成功");
                return rightA;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n    右A线生成异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 连接两条曲线 - 使用AutoCAD的原生方法（严格保持原生特性）
        /// </summary>
        /// <param name="curve1">第一条曲线</param>
        /// <param name="curve2">第二条曲线</param>
        /// <param name="color">颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>连接后的曲线</returns>
        private Curve JoinTwoCurves(Curve curve1, Curve curve2, short color, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n    连接曲线：{curve1.GetType().Name} + {curve2.GetType().Name}");

                // 检查曲线是否可以直接连接（端点重合）
                Point3d curve1End = curve1.EndPoint;
                Point3d curve2Start = curve2.StartPoint;
                double tolerance = 0.001;

                if (curve1End.DistanceTo(curve2Start) > tolerance)
                {
                    editor.WriteMessage($"\n    警告：曲线端点不连续，距离={curve1End.DistanceTo(curve2Start):F6}");
                }

                // 对于直线的特殊处理
                if (curve1 is Line && curve2 is Line)
                {
                    Line line1 = curve1 as Line;
                    Line line2 = curve2 as Line;

                    // 检查是否共线
                    Vector3d dir1 = (line1.EndPoint - line1.StartPoint).GetNormal();
                    Vector3d dir2 = (line2.EndPoint - line2.StartPoint).GetNormal();

                    if (Math.Abs(dir1.DotProduct(dir2)) > 0.999) // 几乎平行
                    {
                        // 创建单一直线
                        Line joinedLine = new Line(line1.StartPoint, line2.EndPoint);
                        joinedLine.ColorIndex = color;
                        editor.WriteMessage($"\n    直线连接成功：共线合并");
                        return joinedLine;
                    }
                    else
                    {
                        // 创建多段线连接两条不共线的直线
                        Polyline polyline = new Polyline();
                        polyline.ColorIndex = color;

                        polyline.AddVertexAt(0, new Point2d(line1.StartPoint.X, line1.StartPoint.Y), 0, 0, 0);
                        polyline.AddVertexAt(1, new Point2d(line1.EndPoint.X, line1.EndPoint.Y), 0, 0, 0);
                        polyline.AddVertexAt(2, new Point2d(line2.EndPoint.X, line2.EndPoint.Y), 0, 0, 0);

                        editor.WriteMessage($"\n    直线连接成功：多段线");
                        return polyline;
                    }
                }
                else
                {
                    // 对于非直线，尝试使用AutoCAD的Join功能
                    // 由于无法直接调用Join命令，使用采样点方法但保持原始特性
                    int sampleCount1 = GetOptimalSampleCount(curve1);
                    int sampleCount2 = GetOptimalSampleCount(curve2);

                    var points1 = SampleCurvePoints(curve1, sampleCount1);
                    var points2 = SampleCurvePoints(curve2, sampleCount2);

                    // 移除重复点
                    if (points2.Count > 0 && points1.Count > 0)
                    {
                        if (points1[points1.Count - 1].DistanceTo(points2[0]) < tolerance)
                        {
                            points2.RemoveAt(0);
                        }
                    }

                    List<Point3d> allPoints = new List<Point3d>();
                    allPoints.AddRange(points1);
                    allPoints.AddRange(points2);

                    if (allPoints.Count >= 2)
                    {
                        // 根据原始曲线类型选择合适的连接方式
                        if (curve1 is Polyline || curve2 is Polyline)
                        {
                            // 如果有多段线，创建多段线
                            Polyline polyline = new Polyline();
                            polyline.ColorIndex = color;

                            for (int i = 0; i < allPoints.Count; i++)
                            {
                                polyline.AddVertexAt(i, new Point2d(allPoints[i].X, allPoints[i].Y), 0, 0, 0);
                            }

                            editor.WriteMessage($"\n    复杂曲线连接成功：多段线，点数={allPoints.Count}");
                            return polyline;
                        }
                        else
                        {
                            // 其他情况创建样条曲线
                            Spline spline = new Spline(new Point3dCollection(allPoints.ToArray()), 3, 0.0);
                            spline.ColorIndex = color;
                            editor.WriteMessage($"\n    复杂曲线连接成功：样条曲线，点数={allPoints.Count}");
                            return spline;
                        }
                    }
                }

                editor.WriteMessage($"\n    曲线连接失败");
                return null;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n连接两条曲线异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 在指定点裁剪曲线 - 使用AutoCAD原生API（严格保持原生特性）
        /// </summary>
        /// <param name="curve">要裁剪的曲线</param>
        /// <param name="trimPoint">裁剪点</param>
        /// <param name="keepStartPart">true保留起始部分，false保留结束部分</param>
        /// <param name="color">颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>裁剪后的曲线</returns>
        private Curve TrimCurveAtPoint(Curve curve, Point3d trimPoint, bool keepStartPart, short color, Editor editor)
        {
            try
            {
                // 找到裁剪点在曲线上的最近点
                Point3d closestPoint = curve.GetClosestPointTo(trimPoint, false);

                editor.WriteMessage($"\n    裁剪曲线：{curve.GetType().Name}");
                editor.WriteMessage($"\n    裁剪点：({trimPoint.X:F3}, {trimPoint.Y:F3})");
                editor.WriteMessage($"\n    最近点：({closestPoint.X:F3}, {closestPoint.Y:F3})");

                try
                {
                    // 尝试使用AutoCAD的原生GetSplitCurves方法
                    double param = curve.GetParameterAtPoint(closestPoint);
                    editor.WriteMessage($"\n    参数值：{param:F6}");

                    var splitCurves = curve.GetSplitCurves(new DoubleCollection(new double[] { param }));

                    if (splitCurves != null && splitCurves.Count >= 2)
                    {
                        Curve resultCurve;
                        if (keepStartPart)
                        {
                            resultCurve = splitCurves[0] as Curve;
                            editor.WriteMessage($"\n    使用原生分割：保留起始部分");
                        }
                        else
                        {
                            resultCurve = splitCurves[splitCurves.Count - 1] as Curve;
                            editor.WriteMessage($"\n    使用原生分割：保留结束部分");
                        }

                        if (resultCurve != null)
                        {
                            resultCurve.ColorIndex = color;
                            return resultCurve;
                        }
                    }
                }
                catch (System.Exception splitEx)
                {
                    editor.WriteMessage($"\n    原生分割失败: {splitEx.Message}，使用备用方法");
                }

                // 备用方法：对于直线使用精确计算
                if (curve is Line)
                {
                    Line line = curve as Line;
                    if (keepStartPart)
                    {
                        Line newLine = new Line(line.StartPoint, closestPoint);
                        newLine.ColorIndex = color;
                        editor.WriteMessage($"\n    直线裁剪：保留起始部分");
                        return newLine;
                    }
                    else
                    {
                        Line newLine = new Line(closestPoint, line.EndPoint);
                        newLine.ColorIndex = color;
                        editor.WriteMessage($"\n    直线裁剪：保留结束部分");
                        return newLine;
                    }
                }
                else
                {
                    // 对于复杂曲线，使用优化的采样方法
                    int sampleCount = GetOptimalSampleCount(curve);
                    var points = SampleCurvePoints(curve, sampleCount);

                    // 找到最接近裁剪点的采样点索引
                    int trimIndex = 0;
                    double minDistance = double.MaxValue;
                    for (int i = 0; i < points.Count; i++)
                    {
                        double distance = points[i].DistanceTo(closestPoint);
                        if (distance < minDistance)
                        {
                            minDistance = distance;
                            trimIndex = i;
                        }
                    }

                    List<Point3d> trimmedPoints = new List<Point3d>();
                    if (keepStartPart)
                    {
                        for (int i = 0; i <= trimIndex; i++)
                        {
                            trimmedPoints.Add(points[i]);
                        }
                    }
                    else
                    {
                        for (int i = trimIndex; i < points.Count; i++)
                        {
                            trimmedPoints.Add(points[i]);
                        }
                    }

                    if (trimmedPoints.Count >= 2)
                    {
                        // 根据原始曲线类型选择合适的结果类型
                        if (curve is Polyline)
                        {
                            Polyline newPolyline = new Polyline();
                            newPolyline.ColorIndex = color;

                            for (int i = 0; i < trimmedPoints.Count; i++)
                            {
                                newPolyline.AddVertexAt(i, new Point2d(trimmedPoints[i].X, trimmedPoints[i].Y), 0, 0, 0);
                            }

                            editor.WriteMessage($"\n    多段线裁剪：点数={trimmedPoints.Count}");
                            return newPolyline;
                        }
                        else
                        {
                            Spline spline = new Spline(new Point3dCollection(trimmedPoints.ToArray()), 3, 0.0);
                            spline.ColorIndex = color;
                            editor.WriteMessage($"\n    样条曲线裁剪：点数={trimmedPoints.Count}");
                            return spline;
                        }
                    }
                }

                editor.WriteMessage($"\n    裁剪失败：无法生成有效结果");
                return null;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n    裁剪曲线异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建左侧铣底区域
        /// </summary>
        /// <param name="leftA">左A线</param>
        /// <param name="dashLineWidth">车道虚线宽度</param>
        /// <param name="singleLaneWidth">单车道宽度</param>
        /// <param name="edgeLineWidth">车道边线宽度</param>
        /// <param name="bikeLineWidth">非机动车道宽度</param>
        /// <param name="millingWidth">铣底宽度</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>左侧铣底区域</returns>
        private Polyline CreateLeftMillingArea(Curve leftA, double dashLineWidth, double singleLaneWidth,
                                             double edgeLineWidth, double bikeLineWidth, double millingWidth,
                                             short millingColor, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n--- 第二步：生成左侧铣底区域 ---");

                // 计算左B线偏移距离
                double leftBOffset = -(dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + bikeLineWidth * 0.5 + (millingWidth - edgeLineWidth) * 0.5);

                // 计算左C线偏移距离
                double leftCOffset = -(dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + bikeLineWidth * 0.5 + bikeLineWidth - (millingWidth - edgeLineWidth) * 0.5);

                editor.WriteMessage($"\n左侧偏移距离计算：");
                editor.WriteMessage($"  基础偏移 = 车道虚线宽度×0.5 + 单车道宽度 + 车道边线宽度 + 非机动车道宽度×0.5");
                editor.WriteMessage($"  基础偏移 = {dashLineWidth * 0.5:F3} + {singleLaneWidth:F3} + {edgeLineWidth:F3} + {bikeLineWidth * 0.5:F3} = {dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + bikeLineWidth * 0.5:F3}");
                editor.WriteMessage($"  左B线偏移 = 基础偏移 + (铣底宽度-车道边线宽度)×0.5 = {-leftBOffset:F3}");
                editor.WriteMessage($"  左C线偏移 = 基础偏移 + 非机动车道宽度 - (铣底宽度-车道边线宽度)×0.5 = {-leftCOffset:F3}");

                // 生成左B线
                Curve leftB = CreateOffsetCurveOnly(leftA, leftBOffset, millingColor, editor, "左B线");
                if (leftB == null)
                {
                    editor.WriteMessage("\n错误：左B线生成失败");
                    return null;
                }

                // 生成左C线
                Curve leftC = CreateOffsetCurveOnly(leftA, leftCOffset, millingColor, editor, "左C线");
                if (leftC == null)
                {
                    editor.WriteMessage("\n错误：左C线生成失败");
                    return null;
                }

                // 连接左B和左C的起始点和结束点，形成闭合区域
                Polyline leftMillingArea = CreateClosedMillingArea(leftB, leftC, millingColor, "左侧铣底区域", editor);

                if (leftMillingArea != null)
                {
                    editor.WriteMessage($"\n左侧铣底区域生成成功，顶点数：{leftMillingArea.NumberOfVertices}");
                }

                return leftMillingArea;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n左侧铣底区域生成异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建右侧铣底区域
        /// </summary>
        /// <param name="rightA">右A线</param>
        /// <param name="dashLineWidth">车道虚线宽度</param>
        /// <param name="singleLaneWidth">单车道宽度</param>
        /// <param name="edgeLineWidth">车道边线宽度</param>
        /// <param name="bikeLineWidth">非机动车道宽度</param>
        /// <param name="millingWidth">铣底宽度</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>右侧铣底区域</returns>
        private Polyline CreateRightMillingArea(Curve rightA, double dashLineWidth, double singleLaneWidth,
                                              double edgeLineWidth, double bikeLineWidth, double millingWidth,
                                              short millingColor, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n--- 第三步：生成右侧铣底区域 ---");

                // 计算右B线偏移距离（正值表示向右偏移）
                double rightBOffset = dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + bikeLineWidth * 0.5 + (millingWidth - edgeLineWidth) * 0.5;

                // 计算右C线偏移距离
                double rightCOffset = dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + bikeLineWidth * 0.5 + bikeLineWidth - (millingWidth - edgeLineWidth) * 0.5;

                editor.WriteMessage($"\n右侧偏移距离计算：");
                editor.WriteMessage($"  基础偏移 = 车道虚线宽度×0.5 + 单车道宽度 + 车道边线宽度 + 非机动车道宽度×0.5");
                editor.WriteMessage($"  基础偏移 = {dashLineWidth * 0.5:F3} + {singleLaneWidth:F3} + {edgeLineWidth:F3} + {bikeLineWidth * 0.5:F3} = {dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + bikeLineWidth * 0.5:F3}");
                editor.WriteMessage($"  右B线偏移 = 基础偏移 + (铣底宽度-车道边线宽度)×0.5 = {rightBOffset:F3}");
                editor.WriteMessage($"  右C线偏移 = 基础偏移 + 非机动车道宽度 - (铣底宽度-车道边线宽度)×0.5 = {rightCOffset:F3}");

                // 生成右B线
                Curve rightB = CreateOffsetCurveOnly(rightA, rightBOffset, millingColor, editor, "右B线");
                if (rightB == null)
                {
                    editor.WriteMessage("\n错误：右B线生成失败");
                    return null;
                }

                // 生成右C线
                Curve rightC = CreateOffsetCurveOnly(rightA, rightCOffset, millingColor, editor, "右C线");
                if (rightC == null)
                {
                    editor.WriteMessage("\n错误：右C线生成失败");
                    return null;
                }

                // 连接右B和右C的起始点和结束点，形成闭合区域
                Polyline rightMillingArea = CreateClosedMillingArea(rightB, rightC, millingColor, "右侧铣底区域", editor);

                if (rightMillingArea != null)
                {
                    editor.WriteMessage($"\n右侧铣底区域生成成功，顶点数：{rightMillingArea.NumberOfVertices}");
                }

                return rightMillingArea;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n右侧铣底区域生成异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建左侧铣底区域（修正版 - 使用原右侧逻辑）
        /// </summary>
        /// <param name="leftA">左A线</param>
        /// <param name="dashLineWidth">车道虚线宽度</param>
        /// <param name="singleLaneWidth">单车道宽度</param>
        /// <param name="edgeLineWidth">车道边线宽度</param>
        /// <param name="bikeLineWidth">非机动车道宽度</param>
        /// <param name="millingWidth">铣底宽度</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>左侧铣底区域</returns>
        private Polyline CreateLeftMillingAreaFixed(Curve leftA, double dashLineWidth, double singleLaneWidth,
                                                   double edgeLineWidth, double bikeLineWidth, double millingWidth,
                                                   short millingColor, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n--- 生成左侧铣底区域（修正版 - 使用原右侧逻辑） ---");

                // 修正：使用新的左B偏移距离公式
                double leftBOffset = dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + (millingWidth - edgeLineWidth) * 0.5;
                double leftCOffset = dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + bikeLineWidth - (millingWidth - edgeLineWidth) * 0.5;

                editor.WriteMessage($"\n左侧偏移距离计算（新公式）：");
                editor.WriteMessage($"  左B线偏移 = 车道虚线宽度×0.5 + 单车道宽度 + 车道边线宽度 + (铣底宽度-车道边线宽度)×0.5");
                editor.WriteMessage($"  左B线偏移 = {dashLineWidth * 0.5:F3} + {singleLaneWidth:F3} + {edgeLineWidth:F3} + {(millingWidth - edgeLineWidth) * 0.5:F3} = {leftBOffset:F3}");
                editor.WriteMessage($"  左C线偏移 = {leftCOffset:F3}");

                // 生成左B线（使用AutoCAD原生offset命令）
                Curve leftB = CreateOffsetCurveOnly(leftA, leftBOffset, millingColor, editor, "左B线（修正）");
                if (leftB == null)
                {
                    editor.WriteMessage("\n错误：左B线（修正）生成失败");
                    return null;
                }

                // 生成左C线（使用AutoCAD原生offset命令）
                Curve leftC = CreateOffsetCurveOnly(leftA, leftCOffset, millingColor, editor, "左C线（修正）");
                if (leftC == null)
                {
                    editor.WriteMessage("\n错误：左C线（修正）生成失败");
                    return null;
                }

                // 直接返回左B线作为铣底线（AutoCAD原生offset结果）
                // 不创建自定义多段线，严格使用AutoCAD原生offset命令的结果
                editor.WriteMessage($"\n左侧铣底区域（修正版）生成成功：直接使用AutoCAD原生offset结果");
                editor.WriteMessage($"\n  左B线类型：{leftB.GetType().Name}");
                editor.WriteMessage($"\n  左C线类型：{leftC.GetType().Name}");

                // 返回左B线作为主要的铣底线（保持AutoCAD原生几何特性）
                return leftB as Polyline ?? CreatePolylineFromCurve(leftB, millingColor, editor);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n左侧铣底区域（修正版）生成异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建右侧铣底区域（修正版 - 使用原左侧逻辑）
        /// </summary>
        /// <param name="rightA">右A线</param>
        /// <param name="dashLineWidth">车道虚线宽度</param>
        /// <param name="singleLaneWidth">单车道宽度</param>
        /// <param name="edgeLineWidth">车道边线宽度</param>
        /// <param name="bikeLineWidth">非机动车道宽度</param>
        /// <param name="millingWidth">铣底宽度</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>右侧铣底区域</returns>
        private Polyline CreateRightMillingAreaFixed(Curve rightA, double dashLineWidth, double singleLaneWidth,
                                                    double edgeLineWidth, double bikeLineWidth, double millingWidth,
                                                    short millingColor, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n--- 生成右侧铣底区域（修正版 - 使用原左侧逻辑） ---");

                // 修正：使用新的右B偏移距离公式（负值表示向左偏移）
                double rightBOffset = -(dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + (millingWidth - edgeLineWidth) * 0.5);
                double rightCOffset = -(dashLineWidth * 0.5 + singleLaneWidth + edgeLineWidth + bikeLineWidth - (millingWidth - edgeLineWidth) * 0.5);

                editor.WriteMessage($"\n右侧偏移距离计算（新公式）：");
                editor.WriteMessage($"  右B线偏移 = -(车道虚线宽度×0.5 + 单车道宽度 + 车道边线宽度 + (铣底宽度-车道边线宽度)×0.5)");
                editor.WriteMessage($"  右B线偏移 = -({dashLineWidth * 0.5:F3} + {singleLaneWidth:F3} + {edgeLineWidth:F3} + {(millingWidth - edgeLineWidth) * 0.5:F3}) = {rightBOffset:F3}");
                editor.WriteMessage($"  右C线偏移 = {rightCOffset:F3}");

                // 生成右B线（使用AutoCAD原生offset命令）
                Curve rightB = CreateOffsetCurveOnly(rightA, rightBOffset, millingColor, editor, "右B线（修正）");
                if (rightB == null)
                {
                    editor.WriteMessage("\n错误：右B线（修正）生成失败");
                    return null;
                }

                // 生成右C线（使用AutoCAD原生offset命令）
                Curve rightC = CreateOffsetCurveOnly(rightA, rightCOffset, millingColor, editor, "右C线（修正）");
                if (rightC == null)
                {
                    editor.WriteMessage("\n错误：右C线（修正）生成失败");
                    return null;
                }

                // 直接返回右B线作为铣底线（AutoCAD原生offset结果）
                // 不创建自定义多段线，严格使用AutoCAD原生offset命令的结果
                editor.WriteMessage($"\n右侧铣底区域（修正版）生成成功：直接使用AutoCAD原生offset结果");
                editor.WriteMessage($"\n  右B线类型：{rightB.GetType().Name}");
                editor.WriteMessage($"\n  右C线类型：{rightC.GetType().Name}");

                // 返回右B线作为主要的铣底线（保持AutoCAD原生几何特性）
                return rightB as Polyline ?? CreatePolylineFromCurve(rightB, millingColor, editor);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n右侧铣底区域（修正版）生成异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 将曲线转换为多段线（仅在必要时使用，优先保持原生曲线类型）
        /// </summary>
        /// <param name="curve">原始曲线</param>
        /// <param name="color">颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>转换后的多段线</returns>
        private Polyline CreatePolylineFromCurve(Curve curve, short color, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n  备用方案：将{curve.GetType().Name}转换为Polyline");

                // 如果已经是多段线，直接返回
                if (curve is Polyline polyline)
                {
                    polyline.ColorIndex = color;
                    return polyline;
                }

                // 对于其他类型，创建简单的多段线表示
                Polyline result = new Polyline();
                result.ColorIndex = color;

                // 添加起点和终点
                result.AddVertexAt(0, new Point2d(curve.StartPoint.X, curve.StartPoint.Y), 0, 0, 0);
                result.AddVertexAt(1, new Point2d(curve.EndPoint.X, curve.EndPoint.Y), 0, 0, 0);

                editor.WriteMessage($"\n  转换完成：{curve.GetType().Name} -> Polyline（2个顶点）");
                return result;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n  曲线转换异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建闭合的铣底区域
        /// </summary>
        /// <param name="innerCurve">内侧曲线</param>
        /// <param name="outerCurve">外侧曲线</param>
        /// <param name="color">颜色</param>
        /// <param name="areaName">区域名称</param>
        /// <param name="editor">编辑器</param>
        /// <returns>闭合的铣底区域</returns>
        private Polyline CreateClosedMillingArea(Curve innerCurve, Curve outerCurve, short color, string areaName, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n  创建{areaName}：");

                Polyline closedArea = new Polyline();
                closedArea.ColorIndex = color;

                // 优化：根据曲线类型动态调整采样点数量
                int sampleCount = GetOptimalSampleCount(innerCurve);

                // 采样内侧曲线的点
                var innerPoints = SampleCurvePoints(innerCurve, sampleCount);
                editor.WriteMessage($"\n    内侧曲线采样点数：{innerPoints.Count}");

                // 采样外侧曲线的点（反向）
                var outerPoints = SampleCurvePoints(outerCurve, sampleCount);
                outerPoints.Reverse();
                editor.WriteMessage($"\n    外侧曲线采样点数：{outerPoints.Count}");

                // 优化：批量添加顶点
                int totalVertices = innerPoints.Count + outerPoints.Count;

                // 添加内侧曲线的点
                for (int i = 0; i < innerPoints.Count; i++)
                {
                    closedArea.AddVertexAt(i, new Point2d(innerPoints[i].X, innerPoints[i].Y), 0, 0, 0);
                }

                // 添加外侧曲线的点（反向）
                int startIndex = closedArea.NumberOfVertices;
                for (int i = 0; i < outerPoints.Count; i++)
                {
                    closedArea.AddVertexAt(startIndex + i, new Point2d(outerPoints[i].X, outerPoints[i].Y), 0, 0, 0);
                }

                // 闭合多段线
                closedArea.Closed = true;

                editor.WriteMessage($"\n    {areaName}创建成功，总顶点数：{closedArea.NumberOfVertices}");

                return closedArea;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建{areaName}异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据曲线类型获取最优采样点数量
        /// </summary>
        /// <param name="curve">曲线</param>
        /// <returns>最优采样点数量</returns>
        private int GetOptimalSampleCount(Curve curve)
        {
            if (curve is Line)
            {
                return 2; // 直线只需要2个点
            }
            else if (curve is Arc)
            {
                return 8; // 圆弧使用8个点
            }
            else if (curve is Polyline)
            {
                Polyline polyline = curve as Polyline;
                return Math.Min(polyline.NumberOfVertices + 2, 12); // 多段线根据顶点数调整，最多12个点
            }
            else
            {
                return 10; // 其他复杂曲线使用10个点（比原来的30个点大幅减少）
            }
        }

        /// <summary>
        /// 创建铣底区域 - 在基准线一侧创建矩形铣底区域
        /// </summary>
        /// <param name="baseLine">基准线</param>
        /// <param name="offsetDistance">偏移距离（负值为左侧，正值为右侧）</param>
        /// <param name="millingWidth">铣底宽度</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="sideName">侧面名称</param>
        /// <param name="editor">编辑器</param>
        /// <returns>铣底区域多段线</returns>
        private Polyline CreateMillingArea(Curve baseLine, double offsetDistance, double millingWidth,
                                         short millingColor, string sideName, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n创建{sideName}铣底区域：");
                editor.WriteMessage($"  偏移距离: {offsetDistance:F3}");
                editor.WriteMessage($"  铣底宽度: {millingWidth:F3}");

                // 第一步：创建内侧偏移线（靠近基准线）
                Curve innerLine = CreateOffsetCurveOnly(baseLine, offsetDistance, millingColor, editor, $"{sideName}内侧");
                if (innerLine == null)
                {
                    editor.WriteMessage($"\n{sideName}内侧偏移失败");
                    return null;
                }

                // 第二步：创建外侧偏移线（远离基准线）
                double outerOffset = offsetDistance + (offsetDistance > 0 ? millingWidth : -millingWidth);
                Curve outerLine = CreateOffsetCurveOnly(baseLine, outerOffset, millingColor, editor, $"{sideName}外侧");
                if (outerLine == null)
                {
                    editor.WriteMessage($"\n{sideName}外侧偏移失败");
                    return null;
                }

                // 第三步：创建闭合的矩形区域
                Polyline millingArea = CreateRectangularArea(innerLine, outerLine, millingColor, $"{sideName}铣底", editor);

                editor.WriteMessage($"\n{sideName}铣底区域创建完成");
                return millingArea;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建{sideName}铣底区域异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建矩形区域 - 基于两条平行线创建闭合矩形
        /// </summary>
        /// <param name="innerLine">内侧线</param>
        /// <param name="outerLine">外侧线</param>
        /// <param name="color">颜色</param>
        /// <param name="areaName">区域名称</param>
        /// <param name="editor">编辑器</param>
        /// <returns>矩形多段线</returns>
        private Polyline CreateRectangularArea(Curve innerLine, Curve outerLine, short color, string areaName, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n  创建{areaName}矩形：");

                Polyline rectangle = new Polyline();
                rectangle.ColorIndex = color;

                // 获取内侧线的起始点和结束点
                Point3d innerStart = innerLine.StartPoint;
                Point3d innerEnd = innerLine.EndPoint;

                // 获取外侧线的起始点和结束点
                Point3d outerStart = outerLine.StartPoint;
                Point3d outerEnd = outerLine.EndPoint;

                // 创建矩形的四个顶点
                // 顺序：内侧起始 -> 内侧结束 -> 外侧结束 -> 外侧起始
                rectangle.AddVertexAt(0, new Point2d(innerStart.X, innerStart.Y), 0, 0, 0);
                rectangle.AddVertexAt(1, new Point2d(innerEnd.X, innerEnd.Y), 0, 0, 0);
                rectangle.AddVertexAt(2, new Point2d(outerEnd.X, outerEnd.Y), 0, 0, 0);
                rectangle.AddVertexAt(3, new Point2d(outerStart.X, outerStart.Y), 0, 0, 0);

                // 闭合矩形
                rectangle.Closed = true;

                editor.WriteMessage($"\n    {areaName}矩形创建成功，顶点数：{rectangle.NumberOfVertices}");
                editor.WriteMessage($"\n    内侧起始点：({innerStart.X:F3}, {innerStart.Y:F3})");
                editor.WriteMessage($"\n    内侧结束点：({innerEnd.X:F3}, {innerEnd.Y:F3})");
                editor.WriteMessage($"\n    外侧起始点：({outerStart.X:F3}, {outerStart.Y:F3})");
                editor.WriteMessage($"\n    外侧结束点：({outerEnd.X:F3}, {outerEnd.Y:F3})");

                return rectangle;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建{areaName}矩形异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建初始偏移操作 - 生成左F和右F线条
        /// </summary>
        /// <param name="startLine">起始线</param>
        /// <param name="dashLineWidth">车道虚线宽度</param>
        /// <param name="singleLaneWidth">单车道宽度</param>
        /// <param name="edgeLineWidth">车道边线宽度</param>
        /// <param name="bikeLineWidth">非机动车道宽度</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>左F和右F线条</returns>
        private (Curve leftF, Curve rightF) CreateInitialMillingOffsets(Curve startLine, double dashLineWidth,
                                                                       double singleLaneWidth, double edgeLineWidth,
                                                                       double bikeLineWidth, short millingColor, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n--- 第一步：初始偏移操作 ---");

                // 计算偏移距离 = 车道虚线宽度×0.5 + 单车道宽度 + 车道边线宽度 + 非机动车道宽度×0.5
                double offsetDistance = (dashLineWidth * 0.5) + singleLaneWidth + edgeLineWidth + (bikeLineWidth * 0.5);

                editor.WriteMessage($"\n偏移距离计算：");
                editor.WriteMessage($"  车道虚线宽度×0.5 = {dashLineWidth:F3} × 0.5 = {dashLineWidth * 0.5:F3}");
                editor.WriteMessage($"  单车道宽度 = {singleLaneWidth:F3}");
                editor.WriteMessage($"  车道边线宽度 = {edgeLineWidth:F3}");
                editor.WriteMessage($"  非机动车道宽度×0.5 = {bikeLineWidth:F3} × 0.5 = {bikeLineWidth * 0.5:F3}");
                editor.WriteMessage($"  总偏移距离 = {offsetDistance:F3}");

                // 使用正确的起始线偏移方式进行偏移
                Curve leftF = CreateOffsetCurveOnly(startLine, -offsetDistance, millingColor, editor, "左F");
                Curve rightF = CreateOffsetCurveOnly(startLine, offsetDistance, millingColor, editor, "右F");

                if (leftF == null || rightF == null)
                {
                    editor.WriteMessage("\n错误：初始偏移操作失败");
                    return (null, null);
                }

                editor.WriteMessage($"\n初始偏移操作成功：");
                editor.WriteMessage($"  左F线类型：{leftF.GetType().Name}");
                editor.WriteMessage($"  右F线类型：{rightF.GetType().Name}");

                return (leftF, rightF);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n初始偏移操作异常: {ex.Message}");
                return (null, null);
            }
        }

        /// <summary>
        /// 直线模式下的处理 - 生成左G和右G线条
        /// </summary>
        /// <param name="leftF">左F线条</param>
        /// <param name="rightF">右F线条</param>
        /// <param name="stopLineWidth">停止线宽度</param>
        /// <param name="millingWidth">铣底宽度</param>
        /// <param name="dashLineWidth">车道虚线宽度</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="btr">块表记录</param>
        /// <param name="trans">事务</param>
        /// <param name="editor">编辑器</param>
        /// <returns>左G和右G线条</returns>
        private (Curve leftG, Curve rightG) ProcessMillingInStraightMode(Curve leftF, Curve rightF,
                                                                         double stopLineWidth, double millingWidth,
                                                                         double dashLineWidth, short millingColor,
                                                                         BlockTableRecord btr, Transaction trans, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n--- 第二步：直线模式下的处理 ---");

                // 计算延长长度 = 停止线宽度 + 铣底宽度×0.5 - 车道虚线宽度×0.5
                double extendLength = stopLineWidth + (millingWidth * 0.5) - (dashLineWidth * 0.5);

                editor.WriteMessage($"\n延长长度计算：");
                editor.WriteMessage($"  停止线宽度 = {stopLineWidth:F3}");
                editor.WriteMessage($"  铣底宽度×0.5 = {millingWidth:F3} × 0.5 = {millingWidth * 0.5:F3}");
                editor.WriteMessage($"  车道虚线宽度×0.5 = {dashLineWidth:F3} × 0.5 = {dashLineWidth * 0.5:F3}");
                editor.WriteMessage($"  延长长度 = {extendLength:F3}");

                // 计算裁剪距离 = 铣底宽度×0.5 - 车道虚线宽度×0.5
                double trimDistance = (millingWidth * 0.5) - (dashLineWidth * 0.5);
                editor.WriteMessage($"\n裁剪距离 = {trimDistance:F3}");

                // 处理左F线
                Curve leftG = ProcessLeftFLine(leftF, extendLength, trimDistance, millingColor, btr, trans, editor);

                // 处理右F线
                Curve rightG = ProcessRightFLine(rightF, extendLength, trimDistance, millingColor, btr, trans, editor);

                if (leftG == null || rightG == null)
                {
                    editor.WriteMessage("\n错误：直线模式处理失败");
                    return (null, null);
                }

                editor.WriteMessage($"\n直线模式处理成功：");
                editor.WriteMessage($"  左G线类型：{leftG.GetType().Name}");
                editor.WriteMessage($"  右G线类型：{rightG.GetType().Name}");

                return (leftG, rightG);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n直线模式处理异常: {ex.Message}");
                return (null, null);
            }
        }

        /// <summary>
        /// 处理左F线 - 在起始点向外延长，在结束点向内裁剪
        /// </summary>
        /// <param name="leftF">左F线条</param>
        /// <param name="extendLength">延长长度</param>
        /// <param name="trimDistance">裁剪距离</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="btr">块表记录</param>
        /// <param name="trans">事务</param>
        /// <param name="editor">编辑器</param>
        /// <returns>处理后的左G线条</returns>
        private Curve ProcessLeftFLine(Curve leftF, double extendLength, double trimDistance, short millingColor,
                                     BlockTableRecord btr, Transaction trans, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n  处理左F线：");

                // 1. 在左F线的起始点向外（起始方向）延长
                Point3d startPoint = leftF.StartPoint;
                Vector3d startDirection = GetTangentAtPoint(leftF, startPoint, true).Negate(); // 向外方向
                Point3d extendStartPoint = startPoint + startDirection * extendLength;

                // 创建延长线段
                Line startExtendLine = new Line(extendStartPoint, startPoint);
                startExtendLine.ColorIndex = millingColor;

                editor.WriteMessage($"\n    起始延长线：从({extendStartPoint.X:F3}, {extendStartPoint.Y:F3})到({startPoint.X:F3}, {startPoint.Y:F3})");

                // 2. 在左F线的结束点向外（结束方向）延长
                Point3d endPoint = leftF.EndPoint;
                Vector3d endDirection = GetTangentAtPoint(leftF, endPoint, false); // 向外方向
                Point3d extendEndPoint = endPoint + endDirection * extendLength;

                // 创建延长线段
                Line endExtendLine = new Line(endPoint, extendEndPoint);
                endExtendLine.ColorIndex = millingColor;

                editor.WriteMessage($"\n    结束延长线：从({endPoint.X:F3}, {endPoint.Y:F3})到({extendEndPoint.X:F3}, {extendEndPoint.Y:F3})");

                // 3. 连接三段：起始延长线 + 原始leftF + 结束延长线
                Curve extendedCurve = JoinThreeCurves(startExtendLine, leftF, endExtendLine, millingColor, editor);

                if (extendedCurve == null)
                {
                    editor.WriteMessage("\n    错误：延长连接操作失败");
                    return null;
                }

                // 4. 从起始点向内标记裁剪点，裁剪距离 = trimDistance
                Point3d newStartPoint = extendedCurve.StartPoint;
                Vector3d trimStartDirection = GetTangentAtPoint(extendedCurve, newStartPoint, true); // 向内方向
                Point3d startTrimPoint = newStartPoint + trimStartDirection * trimDistance;

                // 5. 从结束点向内标记裁剪点，裁剪距离 = trimDistance
                Point3d newEndPoint = extendedCurve.EndPoint;
                Vector3d trimEndDirection = GetTangentAtPoint(extendedCurve, newEndPoint, false).Negate(); // 向内方向
                Point3d endTrimPoint = newEndPoint + trimEndDirection * trimDistance;

                editor.WriteMessage($"\n    起始裁剪点：({startTrimPoint.X:F3}, {startTrimPoint.Y:F3})");
                editor.WriteMessage($"\n    结束裁剪点：({endTrimPoint.X:F3}, {endTrimPoint.Y:F3})");

                // 6. 裁剪两端
                Curve leftG = TrimCurveAtBothEnds(extendedCurve, startTrimPoint, endTrimPoint, millingColor, editor);

                if (leftG == null)
                {
                    editor.WriteMessage("\n    错误：双端裁剪操作失败");
                    return null;
                }

                editor.WriteMessage($"\n    左F线处理完成，生成左G线");
                return leftG;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n    左F线处理异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 处理右F线 - 在起始点向外延长，在结束点向内裁剪
        /// </summary>
        /// <param name="rightF">右F线条</param>
        /// <param name="extendLength">延长长度</param>
        /// <param name="trimDistance">裁剪距离</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="btr">块表记录</param>
        /// <param name="trans">事务</param>
        /// <param name="editor">编辑器</param>
        /// <returns>处理后的右G线条</returns>
        private Curve ProcessRightFLine(Curve rightF, double extendLength, double trimDistance, short millingColor,
                                      BlockTableRecord btr, Transaction trans, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n  处理右F线：");

                // 1. 在右F线的起始点向外（起始方向）延长
                Point3d startPoint = rightF.StartPoint;
                Vector3d startDirection = GetTangentAtPoint(rightF, startPoint, true).Negate(); // 向外方向
                Point3d extendStartPoint = startPoint + startDirection * extendLength;

                // 创建延长线段
                Line startExtendLine = new Line(extendStartPoint, startPoint);
                startExtendLine.ColorIndex = millingColor;

                editor.WriteMessage($"\n    起始延长线：从({extendStartPoint.X:F3}, {extendStartPoint.Y:F3})到({startPoint.X:F3}, {startPoint.Y:F3})");

                // 2. 在右F线的结束点向外（结束方向）延长
                Point3d endPoint = rightF.EndPoint;
                Vector3d endDirection = GetTangentAtPoint(rightF, endPoint, false); // 向外方向
                Point3d extendEndPoint = endPoint + endDirection * extendLength;

                // 创建延长线段
                Line endExtendLine = new Line(endPoint, extendEndPoint);
                endExtendLine.ColorIndex = millingColor;

                editor.WriteMessage($"\n    结束延长线：从({endPoint.X:F3}, {endPoint.Y:F3})到({extendEndPoint.X:F3}, {extendEndPoint.Y:F3})");

                // 3. 连接三段：起始延长线 + 原始rightF + 结束延长线
                Curve extendedCurve = JoinThreeCurves(startExtendLine, rightF, endExtendLine, millingColor, editor);

                if (extendedCurve == null)
                {
                    editor.WriteMessage("\n    错误：延长连接操作失败");
                    return null;
                }

                // 4. 从起始点向内标记裁剪点，裁剪距离 = trimDistance
                Point3d newStartPoint = extendedCurve.StartPoint;
                Vector3d trimStartDirection = GetTangentAtPoint(extendedCurve, newStartPoint, true); // 向内方向
                Point3d startTrimPoint = newStartPoint + trimStartDirection * trimDistance;

                // 5. 从结束点向内标记裁剪点，裁剪距离 = trimDistance
                Point3d newEndPoint = extendedCurve.EndPoint;
                Vector3d trimEndDirection = GetTangentAtPoint(extendedCurve, newEndPoint, false).Negate(); // 向内方向
                Point3d endTrimPoint = newEndPoint + trimEndDirection * trimDistance;

                editor.WriteMessage($"\n    起始裁剪点：({startTrimPoint.X:F3}, {startTrimPoint.Y:F3})");
                editor.WriteMessage($"\n    结束裁剪点：({endTrimPoint.X:F3}, {endTrimPoint.Y:F3})");

                // 6. 裁剪两端
                Curve rightG = TrimCurveAtBothEnds(extendedCurve, startTrimPoint, endTrimPoint, millingColor, editor);

                if (rightG == null)
                {
                    editor.WriteMessage("\n    错误：双端裁剪操作失败");
                    return null;
                }

                editor.WriteMessage($"\n    右F线处理完成，生成右G线");
                return rightG;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n    右F线处理异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 二次偏移操作 - 生成左H、左I、右H、右I四条线
        /// </summary>
        /// <param name="leftG">左G线条</param>
        /// <param name="rightG">右G线条</param>
        /// <param name="bikeLineWidth">非机动车道宽度</param>
        /// <param name="millingWidth">铣底宽度</param>
        /// <param name="edgeLineWidth">车道边线宽度</param>
        /// <param name="millingColor">铣底颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>四条偏移线条</returns>
        private (Curve leftH, Curve leftI, Curve rightH, Curve rightI) CreateSecondaryMillingOffsets(
            Curve leftG, Curve rightG, double bikeLineWidth, double millingWidth, double edgeLineWidth,
            short millingColor, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n--- 第三步：二次偏移操作 ---");

                // 计算偏移距离 = (非机动车道宽度 - 铣底宽度 + 车道边线宽度) × 0.5
                double offsetDistance = (bikeLineWidth - millingWidth + edgeLineWidth) * 0.5;

                editor.WriteMessage($"\n偏移距离计算：");
                editor.WriteMessage($"  非机动车道宽度 = {bikeLineWidth:F3}");
                editor.WriteMessage($"  铣底宽度 = {millingWidth:F3}");
                editor.WriteMessage($"  车道边线宽度 = {edgeLineWidth:F3}");
                editor.WriteMessage($"  偏移距离 = ({bikeLineWidth:F3} - {millingWidth:F3} + {edgeLineWidth:F3}) × 0.5 = {offsetDistance:F3}");

                // 左G向左偏移距离，命名为"左H"
                Curve leftH = CreateOffsetCurveOnly(leftG, -offsetDistance, millingColor, editor, "左H");

                // 左G向右偏移距离，命名为"左I"
                Curve leftI = CreateOffsetCurveOnly(leftG, offsetDistance, millingColor, editor, "左I");

                // 右G向左偏移距离，命名为"右I"
                Curve rightI = CreateOffsetCurveOnly(rightG, -offsetDistance, millingColor, editor, "右I");

                // 右G向右偏移距离，命名为"右H"
                Curve rightH = CreateOffsetCurveOnly(rightG, offsetDistance, millingColor, editor, "右H");

                if (leftH == null || leftI == null || rightH == null || rightI == null)
                {
                    editor.WriteMessage("\n错误：二次偏移操作失败");
                    return (null, null, null, null);
                }

                editor.WriteMessage($"\n二次偏移操作成功：");
                editor.WriteMessage($"  左H线类型：{leftH.GetType().Name}");
                editor.WriteMessage($"  左I线类型：{leftI.GetType().Name}");
                editor.WriteMessage($"  右H线类型：{rightH.GetType().Name}");
                editor.WriteMessage($"  右I线类型：{rightI.GetType().Name}");

                return (leftH, leftI, rightH, rightI);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n二次偏移操作异常: {ex.Message}");
                return (null, null, null, null);
            }
        }

        /// <summary>
        /// 清理和闭合操作 - 删除中间线条，连接形成闭合图形
        /// </summary>
        /// <param name="leftG">左G线条（需要删除）</param>
        /// <param name="rightG">右G线条（需要删除）</param>
        /// <param name="millingLines">四条偏移线条</param>
        /// <param name="btr">块表记录</param>
        /// <param name="trans">事务</param>
        /// <param name="editor">编辑器</param>
        private void CleanupAndCloseMillingShapes((Curve leftH, Curve leftI, Curve rightH, Curve rightI) millingLines,
                                                 BlockTableRecord btr, Transaction trans, Editor editor)
        {
            try
            {
                editor.WriteMessage("\n--- 第四步：清理和闭合操作 ---");

                // 1. 删除左G和右G线条（这些是中间线条，不需要保留）
                // 注意：leftG和rightG是临时创建的，不需要从数据库中删除
                editor.WriteMessage("\n  中间线条已清理（leftG和rightG）");

                // 2. 连接左H和左I的起始点和结束点，形成闭合图形
                Polyline leftClosedShape = CreateClosedShape(millingLines.leftH, millingLines.leftI,
                                                            (short)millingLines.leftH.ColorIndex, "左侧铣底区域", editor);

                // 3. 连接右H和右I的起始点和结束点，形成闭合图形
                Polyline rightClosedShape = CreateClosedShape(millingLines.rightH, millingLines.rightI,
                                                             (short)millingLines.rightH.ColorIndex, "右侧铣底区域", editor);

                // 4. 将闭合图形添加到图形中
                if (leftClosedShape != null)
                {
                    ObjectId leftId = btr.AppendEntity(leftClosedShape);
                    trans.AddNewlyCreatedDBObject(leftClosedShape, true);
                    editor.WriteMessage($"\n  左侧铣底区域已添加，ObjectId: {leftId}");
                }

                if (rightClosedShape != null)
                {
                    ObjectId rightId = btr.AppendEntity(rightClosedShape);
                    trans.AddNewlyCreatedDBObject(rightClosedShape, true);
                    editor.WriteMessage($"\n  右侧铣底区域已添加，ObjectId: {rightId}");
                }

                editor.WriteMessage("\n  清理和闭合操作完成");
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n清理和闭合操作异常: {ex.Message}");
            }
        }

        // ===== 铣底功能辅助方法 =====

        /// <summary>
        /// 连接两条曲线 - 使用AutoCAD的Join命令或创建复合曲线
        /// </summary>
        /// <param name="curve1">第一条曲线</param>
        /// <param name="curve2">第二条曲线</param>
        /// <param name="color">颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>连接后的曲线</returns>
        private Curve JoinCurves(Curve curve1, Curve curve2, short color, Editor editor)
        {
            try
            {
                // 尝试使用AutoCAD的偏移功能来连接曲线
                DBObjectCollection curves = new DBObjectCollection();
                curves.Add(curve1.Clone() as DBObject);
                curves.Add(curve2.Clone() as DBObject);

                // 尝试使用GetOffsetCurves方法（零偏移）来保持几何特性
                DBObjectCollection offsetCurves = curve1.GetOffsetCurves(0);
                if (offsetCurves != null && offsetCurves.Count > 0)
                {
                    // 如果偏移成功，使用第一个结果
                    Curve result = offsetCurves[0] as Curve;
                    if (result != null)
                    {
                        result.ColorIndex = color;

                        // 清理其他结果
                        for (int i = 1; i < offsetCurves.Count; i++)
                        {
                            offsetCurves[i].Dispose();
                        }

                        return result;
                    }
                }

                // 如果AutoCAD的偏移失败，创建复合曲线
                return CreateCompositeCurve(curve1, curve2, color, editor);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n连接曲线异常: {ex.Message}");
                // 备用方法：创建复合曲线
                return CreateCompositeCurve(curve1, curve2, color, editor);
            }
        }

        /// <summary>
        /// 采样曲线上的点
        /// </summary>
        /// <param name="curve">曲线</param>
        /// <param name="numPoints">采样点数量</param>
        /// <returns>采样点列表</returns>
        private List<Point3d> SampleCurvePoints(Curve curve, int numPoints)
        {
            List<Point3d> points = new List<Point3d>();

            try
            {
                double startParam = curve.StartParam;
                double endParam = curve.EndParam;
                double step = (endParam - startParam) / (numPoints - 1);

                for (int i = 0; i < numPoints; i++)
                {
                    double param = startParam + i * step;
                    Point3d point = curve.GetPointAtParameter(param);
                    points.Add(point);
                }
            }
            catch
            {
                // 如果参数化失败，使用起始和结束点
                points.Add(curve.StartPoint);
                points.Add(curve.EndPoint);
            }

            return points;
        }



        /// <summary>
        /// 创建闭合图形
        /// </summary>
        /// <param name="curve1">第一条曲线</param>
        /// <param name="curve2">第二条曲线</param>
        /// <param name="color">颜色</param>
        /// <param name="shapeName">图形名称</param>
        /// <param name="editor">编辑器</param>
        /// <returns>闭合的多段线</returns>
        private Polyline CreateClosedShape(Curve curve1, Curve curve2, short color, string shapeName, Editor editor)
        {
            try
            {
                editor.WriteMessage($"\n  创建{shapeName}：");

                Polyline closedShape = new Polyline();
                closedShape.ColorIndex = color;

                // 采样第一条曲线的点
                var points1 = SampleCurvePoints(curve1, 20);

                // 采样第二条曲线的点（反向）
                var points2 = SampleCurvePoints(curve2, 20);
                points2.Reverse();

                // 添加第一条曲线的点
                for (int i = 0; i < points1.Count; i++)
                {
                    closedShape.AddVertexAt(i, new Point2d(points1[i].X, points1[i].Y), 0, 0, 0);
                }

                // 添加第二条曲线的点
                int startIndex = closedShape.NumberOfVertices;
                for (int i = 0; i < points2.Count; i++)
                {
                    closedShape.AddVertexAt(startIndex + i, new Point2d(points2[i].X, points2[i].Y), 0, 0, 0);
                }

                // 闭合多段线
                closedShape.Closed = true;

                editor.WriteMessage($"\n    {shapeName}创建成功，顶点数：{closedShape.NumberOfVertices}");

                return closedShape;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建{shapeName}异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 仅创建偏移曲线，不添加到图形（用于后续处理）
        /// </summary>
        /// <param name="startLine">原始基准线</param>
        /// <param name="offsetValue">偏移值</param>
        /// <param name="millingColor">偏移线条颜色</param>
        /// <param name="editor">编辑器</param>
        /// <param name="sideName">偏移方向名称</param>
        /// <returns>偏移后的曲线（未添加到图形）</returns>
        /// <summary>
        /// 仅创建偏移曲线，不添加到图形（严格使用AutoCAD原生offset命令）
        /// </summary>
        /// <param name="startLine">原始基准线</param>
        /// <param name="offsetValue">偏移值</param>
        /// <param name="millingColor">偏移线条颜色</param>
        /// <param name="editor">编辑器</param>
        /// <param name="sideName">偏移方向名称</param>
        /// <returns>偏移后的曲线（未添加到图形）</returns>
        private Curve CreateOffsetCurveOnly(Curve startLine, double offsetValue, short millingColor, Editor editor, string sideName = "")
        {
            try
            {
                editor.WriteMessage($"\n创建{sideName}偏移曲线，距离: {offsetValue:F3}mm");

                // 对于非直线类型，需要修正左右方向（左变右，右变左）
                double actualOffsetValue = offsetValue;
                bool needDirectionCorrection = !(startLine is Line);

                if (needDirectionCorrection)
                {
                    actualOffsetValue = -offsetValue; // 反转偏移方向
                    editor.WriteMessage($"\n  检测到{startLine.GetType().Name}，应用方向修正：{offsetValue:F3} -> {actualOffsetValue:F3}");
                }

                // 严格使用AutoCAD的GetOffsetCurves方法进行偏移（等同于OFFSET命令）
                DBObjectCollection offsetCurves = startLine.GetOffsetCurves(actualOffsetValue);

                if (offsetCurves == null || offsetCurves.Count == 0)
                {
                    editor.WriteMessage($"\n{sideName}偏移操作失败：无法生成偏移曲线");
                    return null;
                }

                // 获取第一个偏移结果（这是AutoCAD原生的offset结果，保持原始几何特性）
                Curve offsetLine = (Curve)offsetCurves[0];
                offsetLine.ColorIndex = millingColor;

                // 清理其他偏移结果
                for (int i = 1; i < offsetCurves.Count; i++)
                {
                    offsetCurves[i].Dispose();
                }

                string correctionInfo = needDirectionCorrection ? "（已修正方向）" : "";
                editor.WriteMessage($"\n  {sideName}偏移曲线创建成功{correctionInfo}: {startLine.GetType().Name} -> {offsetLine.GetType().Name}");
                editor.WriteMessage($"\n  返回的是AutoCAD原生offset结果，保持完整几何特性");

                return offsetLine;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n{sideName}偏移曲线创建异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建复合曲线 - 保持原始几何特性
        /// </summary>
        /// <param name="curve1">第一条曲线</param>
        /// <param name="curve2">第二条曲线</param>
        /// <param name="color">颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>复合曲线</returns>
        private Curve CreateCompositeCurve(Curve curve1, Curve curve2, short color, Editor editor)
        {
            try
            {
                // 对于简单的直线连接，创建多段线
                if (curve1 is Line && curve2 is Line)
                {
                    Polyline polyline = new Polyline();
                    polyline.ColorIndex = color;

                    Line line1 = curve1 as Line;
                    Line line2 = curve2 as Line;

                    polyline.AddVertexAt(0, new Point2d(line1.StartPoint.X, line1.StartPoint.Y), 0, 0, 0);
                    polyline.AddVertexAt(1, new Point2d(line1.EndPoint.X, line1.EndPoint.Y), 0, 0, 0);
                    polyline.AddVertexAt(2, new Point2d(line2.EndPoint.X, line2.EndPoint.Y), 0, 0, 0);

                    return polyline;
                }
                else
                {
                    // 对于复杂曲线，保持原始特性，创建样条曲线
                    var points1 = SampleCurvePoints(curve1, 20);
                    var points2 = SampleCurvePoints(curve2, 20);

                    // 移除重复点
                    if (points2.Count > 0 && points1.Count > 0)
                    {
                        if (points1[points1.Count - 1].DistanceTo(points2[0]) < 0.001)
                        {
                            points2.RemoveAt(0);
                        }
                    }

                    List<Point3d> allPoints = new List<Point3d>();
                    allPoints.AddRange(points1);
                    allPoints.AddRange(points2);

                    if (allPoints.Count >= 2)
                    {
                        // 创建样条曲线以保持平滑性
                        Spline spline = new Spline(new Point3dCollection(allPoints.ToArray()), 3, 0.0);
                        spline.ColorIndex = color;
                        return spline;
                    }
                }

                return null;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n创建复合曲线异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 连接三条曲线
        /// </summary>
        /// <param name="curve1">第一条曲线</param>
        /// <param name="curve2">第二条曲线</param>
        /// <param name="curve3">第三条曲线</param>
        /// <param name="color">颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>连接后的曲线</returns>
        private Curve JoinThreeCurves(Curve curve1, Curve curve2, Curve curve3, short color, Editor editor)
        {
            try
            {
                // 先连接前两条曲线
                Curve temp = JoinCurves(curve1, curve2, color, editor);
                if (temp == null)
                {
                    return null;
                }

                // 再连接第三条曲线
                return JoinCurves(temp, curve3, color, editor);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n连接三条曲线异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 在两个点处裁剪曲线
        /// </summary>
        /// <param name="curve">要裁剪的曲线</param>
        /// <param name="startTrimPoint">起始裁剪点</param>
        /// <param name="endTrimPoint">结束裁剪点</param>
        /// <param name="color">颜色</param>
        /// <param name="editor">编辑器</param>
        /// <returns>裁剪后的曲线</returns>
        private Curve TrimCurveAtBothEnds(Curve curve, Point3d startTrimPoint, Point3d endTrimPoint, short color, Editor editor)
        {
            try
            {
                // 找到最接近裁剪点的参数
                Point3d closestStartPoint = curve.GetClosestPointTo(startTrimPoint, false);
                Point3d closestEndPoint = curve.GetClosestPointTo(endTrimPoint, false);

                double startTrimParam = curve.GetParameterAtPoint(closestStartPoint);
                double endTrimParam = curve.GetParameterAtPoint(closestEndPoint);

                // 确保参数顺序正确
                if (startTrimParam > endTrimParam)
                {
                    double temp = startTrimParam;
                    startTrimParam = endTrimParam;
                    endTrimParam = temp;
                }

                // 使用分割方法获取中间部分
                var splitParams = new DoubleCollection(new double[] { startTrimParam, endTrimParam });
                var splitCurves = curve.GetSplitCurves(splitParams);

                if (splitCurves.Count >= 2)
                {
                    // 获取中间部分（索引1）
                    Curve trimmedCurve = splitCurves[1] as Curve;
                    if (trimmedCurve != null)
                    {
                        trimmedCurve.ColorIndex = color;

                        // 清理其他分割结果
                        for (int i = 0; i < splitCurves.Count; i++)
                        {
                            if (i != 1)
                            {
                                splitCurves[i].Dispose();
                            }
                        }

                        return trimmedCurve;
                    }
                }

                // 如果分割失败，返回原曲线的副本
                Curve clonedCurve = curve.Clone() as Curve;
                if (clonedCurve != null)
                {
                    clonedCurve.ColorIndex = color;
                }
                return clonedCurve;
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n双端裁剪曲线异常: {ex.Message}");
                // 如果裁剪失败，返回原曲线的副本
                Curve clonedCurve = curve.Clone() as Curve;
                if (clonedCurve != null)
                {
                    clonedCurve.ColorIndex = color;
                }
                return clonedCurve;
            }
        }
    }
}
